// ===== REVOLUTIONARY MAIN JAVASCRIPT =====

class GrumpyPortfolio {
    constructor() {
        this.isLoaded = false;
        this.loadingProgress = 0;
        this.morphingWords = ['THE FUTURE', 'IMPOSSIBLE', 'EXTRAORDINARY', 'REVOLUTIONARY', 'CUTTING-EDGE'];
        this.currentWordIndex = 0;
        
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.initializeLoader();
        this.initializeGSAP();
        this.initializeScrollTriggers();
        this.initializeMorphingText();
        this.initializeParticles();
        this.initializeWebGL();
        this.initializeIntersectionObserver();
        this.initializeNavigation();
        this.initializeCounters();
        this.initializeRippleEffects();
    }

    setupEventListeners() {
        window.addEventListener('load', () => this.handleWindowLoad());
        window.addEventListener('scroll', () => this.handleScroll());
        window.addEventListener('resize', () => this.handleResize());
        document.addEventListener('DOMContentLoaded', () => this.handleDOMLoaded());
    }

    // ===== ADVANCED LOADING SYSTEM =====
    initializeLoader() {
        const loader = document.getElementById('loader-system');
        const progressBar = document.querySelector('.progress-bar');
        const progressNumber = document.querySelector('.progress-number');
        const loaderCanvas = document.getElementById('loader-canvas');
        
        if (!loader || !progressBar || !progressNumber || !loaderCanvas) return;

        // Initialize loader canvas
        this.initializeLoaderCanvas(loaderCanvas);
        
        // Simulate loading progress
        this.simulateLoading(progressBar, progressNumber);
        
        // Preload critical resources
        this.preloadResources();
    }

    initializeLoaderCanvas(canvas) {
        const ctx = canvas.getContext('2d');
        canvas.width = window.innerWidth;
        canvas.height = window.innerHeight;
        
        const particles = [];
        const particleCount = 50;
        
        // Create particles
        for (let i = 0; i < particleCount; i++) {
            particles.push({
                x: Math.random() * canvas.width,
                y: Math.random() * canvas.height,
                vx: (Math.random() - 0.5) * 2,
                vy: (Math.random() - 0.5) * 2,
                size: Math.random() * 3 + 1,
                opacity: Math.random() * 0.5 + 0.2
            });
        }
        
        const animate = () => {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            particles.forEach(particle => {
                particle.x += particle.vx;
                particle.y += particle.vy;
                
                if (particle.x < 0 || particle.x > canvas.width) particle.vx *= -1;
                if (particle.y < 0 || particle.y > canvas.height) particle.vy *= -1;
                
                ctx.beginPath();
                ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
                ctx.fillStyle = `rgba(255, 195, 33, ${particle.opacity})`;
                ctx.fill();
            });
            
            if (!this.isLoaded) {
                requestAnimationFrame(animate);
            }
        };
        
        animate();
    }

    simulateLoading(progressBar, progressNumber) {
        const duration = 3000; // 3 seconds
        const startTime = Date.now();
        
        const updateProgress = () => {
            const elapsed = Date.now() - startTime;
            const progress = Math.min((elapsed / duration) * 100, 100);
            
            progressBar.style.width = `${progress}%`;
            progressNumber.textContent = Math.floor(progress);
            this.loadingProgress = progress;
            
            if (progress < 100) {
                requestAnimationFrame(updateProgress);
            } else {
                setTimeout(() => this.hideLoader(), 500);
            }
        };
        
        updateProgress();
    }

    preloadResources() {
        const resources = [
            'images/logo-black.png',
            'fonts/BricolageGrotesque-Black.woff2',
            'fonts/BricolageGrotesque-Bold.woff2'
        ];
        
        resources.forEach(src => {
            if (src.includes('.woff2')) {
                const font = new FontFace('NewBlackTypeface', `url(${src})`);
                font.load().then(loadedFont => {
                    document.fonts.add(loadedFont);
                });
            } else {
                const img = new Image();
                img.src = src;
            }
        });
    }

    hideLoader() {
        const loader = document.getElementById('loader-system');
        if (loader) {
            loader.classList.add('hidden');
            setTimeout(() => {
                loader.style.display = 'none';
                this.isLoaded = true;
                this.startMainAnimations();
            }, 800);
        }
    }

    // ===== GSAP ANIMATIONS =====
    initializeGSAP() {
        gsap.registerPlugin(ScrollTrigger, TextPlugin);
        
        // Set initial states
        gsap.set('.hero-badge', { opacity: 0, y: 30 });
        gsap.set('.hero-title .word', { opacity: 0, y: 100 });
        gsap.set('.hero-subtitle .subtitle-line', { opacity: 0, y: 50 });
        gsap.set('.hero-actions .btn', { opacity: 0, y: 30, scale: 0.8 });
        gsap.set('.hero-scroll', { opacity: 0 });
    }

    startMainAnimations() {
        const tl = gsap.timeline();
        
        tl.to('.hero-badge', {
            opacity: 1,
            y: 0,
            duration: 0.8,
            ease: 'power3.out'
        })
        .to('.hero-title .word', {
            opacity: 1,
            y: 0,
            duration: 1,
            stagger: 0.2,
            ease: 'power3.out'
        }, '-=0.4')
        .to('.hero-subtitle .subtitle-line', {
            opacity: 1,
            y: 0,
            duration: 0.8,
            stagger: 0.1,
            ease: 'power2.out'
        }, '-=0.6')
        .to('.hero-actions .btn', {
            opacity: 1,
            y: 0,
            scale: 1,
            duration: 0.6,
            stagger: 0.1,
            ease: 'back.out(1.7)'
        }, '-=0.4')
        .to('.hero-scroll', {
            opacity: 1,
            duration: 0.5,
            ease: 'power2.out'
        }, '-=0.2');
    }

    initializeScrollTriggers() {
        // Fade in animations for sections
        gsap.utils.toArray('.fade-in').forEach(element => {
            gsap.fromTo(element, 
                { opacity: 0, y: 50 },
                {
                    opacity: 1,
                    y: 0,
                    duration: 1,
                    ease: 'power2.out',
                    scrollTrigger: {
                        trigger: element,
                        start: 'top 80%',
                        end: 'bottom 20%',
                        toggleActions: 'play none none reverse'
                    }
                }
            );
        });

        // Service cards animation
        gsap.utils.toArray('.service-card').forEach((card, index) => {
            gsap.fromTo(card,
                { opacity: 0, y: 80, scale: 0.8 },
                {
                    opacity: 1,
                    y: 0,
                    scale: 1,
                    duration: 0.8,
                    ease: 'power3.out',
                    scrollTrigger: {
                        trigger: card,
                        start: 'top 85%',
                        end: 'bottom 15%',
                        toggleActions: 'play none none reverse'
                    },
                    delay: index * 0.1
                }
            );
        });

        // Stats counter animation
        gsap.utils.toArray('.stat-number').forEach(stat => {
            const target = parseInt(stat.getAttribute('data-target'));
            gsap.fromTo(stat,
                { textContent: 0 },
                {
                    textContent: target,
                    duration: 2,
                    ease: 'power2.out',
                    snap: { textContent: 1 },
                    scrollTrigger: {
                        trigger: stat,
                        start: 'top 80%',
                        toggleActions: 'play none none none'
                    }
                }
            );
        });
    }

    // ===== MORPHING TEXT ANIMATION =====
    initializeMorphingText() {
        const morphingElement = document.getElementById('morphing-word');
        if (!morphingElement) return;

        setInterval(() => {
            this.morphToNextWord(morphingElement);
        }, 3000);
    }

    morphToNextWord(element) {
        const currentWord = this.morphingWords[this.currentWordIndex];
        const nextIndex = (this.currentWordIndex + 1) % this.morphingWords.length;
        const nextWord = this.morphingWords[nextIndex];
        
        // Animate out current word
        gsap.to(element.querySelectorAll('.char'), {
            y: -50,
            opacity: 0,
            duration: 0.3,
            stagger: 0.02,
            ease: 'power2.in',
            onComplete: () => {
                // Update text content
                element.innerHTML = nextWord.split('').map(char => 
                    `<span class="char">${char}</span>`
                ).join('');
                
                // Animate in new word
                gsap.fromTo(element.querySelectorAll('.char'), 
                    { y: 50, opacity: 0 },
                    {
                        y: 0,
                        opacity: 1,
                        duration: 0.4,
                        stagger: 0.02,
                        ease: 'power2.out'
                    }
                );
                
                this.currentWordIndex = nextIndex;
            }
        });
    }

    // ===== NAVIGATION =====
    initializeNavigation() {
        const nav = document.getElementById('main-nav');
        const navToggle = document.getElementById('nav-toggle');
        
        if (!nav) return;

        // Smooth scroll for navigation links
        document.querySelectorAll('.nav-link[href^="#"]').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const target = document.querySelector(link.getAttribute('href'));
                if (target) {
                    gsap.to(window, {
                        duration: 1,
                        scrollTo: { y: target, offsetY: 80 },
                        ease: 'power2.inOut'
                    });
                }
            });
        });

        // Mobile navigation toggle
        if (navToggle) {
            navToggle.addEventListener('click', () => {
                nav.classList.toggle('mobile-open');
            });
        }
    }

    handleScroll() {
        const nav = document.getElementById('main-nav');
        if (nav) {
            if (window.scrollY > 100) {
                nav.classList.add('scrolled');
            } else {
                nav.classList.remove('scrolled');
            }
        }
    }

    // ===== COUNTER ANIMATIONS =====
    initializeCounters() {
        const counters = document.querySelectorAll('.stat-number[data-target]');
        
        counters.forEach(counter => {
            const target = parseInt(counter.getAttribute('data-target'));
            let current = 0;
            const increment = target / 100;
            
            const updateCounter = () => {
                if (current < target) {
                    current += increment;
                    counter.textContent = Math.ceil(current);
                    requestAnimationFrame(updateCounter);
                } else {
                    counter.textContent = target;
                }
            };
            
            // Start counter when element is visible
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        updateCounter();
                        observer.unobserve(entry.target);
                    }
                });
            });
            
            observer.observe(counter);
        });
    }

    // ===== RIPPLE EFFECTS =====
    initializeRippleEffects() {
        document.querySelectorAll('.btn').forEach(button => {
            button.addEventListener('click', (e) => {
                const ripple = button.querySelector('.btn-ripple');
                if (ripple) {
                    const rect = button.getBoundingClientRect();
                    const size = Math.max(rect.width, rect.height);
                    const x = e.clientX - rect.left - size / 2;
                    const y = e.clientY - rect.top - size / 2;
                    
                    ripple.style.width = ripple.style.height = size + 'px';
                    ripple.style.left = x + 'px';
                    ripple.style.top = y + 'px';
                    
                    ripple.classList.add('animate');
                    setTimeout(() => ripple.classList.remove('animate'), 600);
                }
            });
        });
    }

    // ===== INTERSECTION OBSERVER =====
    initializeIntersectionObserver() {
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('visible');
                }
            });
        }, observerOptions);

        document.querySelectorAll('.fade-in, .slide-up, .scale-in').forEach(el => {
            observer.observe(el);
        });
    }

    // ===== PLACEHOLDER METHODS FOR EXTERNAL FILES =====
    initializeParticles() {
        // This will be implemented in particles.js
        if (window.ParticleSystem) {
            this.particleSystem = new ParticleSystem();
        }
    }

    initializeWebGL() {
        // This will be implemented in webgl.js
        if (window.WebGLBackground) {
            this.webglBackground = new WebGLBackground();
        }
    }

    // ===== EVENT HANDLERS =====
    handleWindowLoad() {
        document.body.classList.add('loaded');
    }

    handleDOMLoaded() {
        document.body.classList.add('dom-loaded');
    }

    handleResize() {
        // Handle responsive adjustments
        if (this.particleSystem) {
            this.particleSystem.handleResize();
        }
        if (this.webglBackground) {
            this.webglBackground.handleResize();
        }
    }
}

// Initialize the portfolio when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.grumpyPortfolio = new GrumpyPortfolio();
});

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = GrumpyPortfolio;
}
