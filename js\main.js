// ===== GSAP REGISTRATION =====
gsap.registerPlugin(ScrollTrigger, TextPlugin);

// ===== GLOBAL VARIABLES =====
let isDesktop = window.innerWidth > 768;

// ===== LOADING ANIMATION =====
function initLoader() {
    const loader = document.getElementById('loader');
    const loaderLogo = document.querySelector('.loader-logo-img');
    const loaderWords = document.querySelectorAll('.loader-word');
    const progressBar = document.querySelector('.progress-bar');
    const progressText = document.querySelector('.progress-text');

    // Animate loader elements
    const tl = gsap.timeline();

    // Logo animation
    tl.to(loaderLogo, {
        opacity: 1,
        scale: 1,
        duration: 0.8,
        ease: "back.out(1.7)"
    });

    // Words animation
    tl.fromTo(loaderWords, {
        opacity: 0,
        y: 30
    }, {
        opacity: 1,
        y: 0,
        duration: 0.6,
        stagger: 0.2,
        ease: "power2.out"
    }, "-=0.3");

    // Progress bar animation
    let progress = 0;
    const progressInterval = setInterval(() => {
        progress += Math.random() * 15 + 5;
        if (progress > 100) progress = 100;

        progressBar.style.setProperty('--progress', progress + '%');
        progressText.textContent = Math.round(progress) + '%';

        if (progress >= 100) {
            clearInterval(progressInterval);
            setTimeout(() => {
                gsap.to(loader, {
                    opacity: 0,
                    duration: 0.5,
                    ease: "power2.inOut",
                    onComplete: () => {
                        loader.style.display = 'none';
                        document.body.style.overflow = 'auto';
                        initPageAnimations();
                    }
                });
            }, 500);
        }
    }, 100);

    // Update progress bar CSS
    progressBar.style.setProperty('--progress', '0%');
}

// ===== MORPHING TEXT ANIMATION =====
function initMorphingText() {
    const morphingWord = document.querySelector('.morphing-word');
    if (!morphingWord) return;

    const chars = morphingWord.querySelectorAll('.char');

    // Initial animation
    gsap.fromTo(chars, {
        y: 100,
        opacity: 0,
        rotation: 15
    }, {
        y: 0,
        opacity: 1,
        rotation: 0,
        duration: 1,
        stagger: 0.1,
        ease: "back.out(1.7)",
        delay: 0.5
    });

    // Continuous morphing animation
    const words = ['Anything', 'Websites', 'Brands', 'Dreams', 'Magic', 'Future'];
    let currentIndex = 0;

    setInterval(() => {
        currentIndex = (currentIndex + 1) % words.length;
        const newWord = words[currentIndex];

        // Animate out current chars
        gsap.to(chars, {
            y: -50,
            opacity: 0,
            rotation: -15,
            duration: 0.5,
            stagger: 0.05,
            ease: "power2.in",
            onComplete: () => {
                // Update text content
                newWord.split('').forEach((char, index) => {
                    if (chars[index]) {
                        chars[index].textContent = char;
                    }
                });

                // Animate in new chars
                gsap.fromTo(chars, {
                    y: 50,
                    opacity: 0,
                    rotation: 15
                }, {
                    y: 0,
                    opacity: 1,
                    rotation: 0,
                    duration: 0.5,
                    stagger: 0.05,
                    ease: "back.out(1.7)"
                });
            }
        });
    }, 4000);
}

// ===== PAGE ANIMATIONS =====
function initPageAnimations() {
    initHeroAnimations();
    initMorphingText();
    initScrollAnimations();
    initInteractiveElements();
}

// ===== HERO ANIMATIONS =====
function initHeroAnimations() {
    const tl = gsap.timeline();

    // Hero badge
    tl.fromTo('.hero-badge', {
        y: 50,
        opacity: 0,
        scale: 0.8
    }, {
        y: 0,
        opacity: 1,
        scale: 1,
        duration: 1,
        ease: "back.out(1.7)"
    });

    // Hero title words with underline effect
    tl.fromTo('.title-line .word', {
        y: 100,
        opacity: 0
    }, {
        y: 0,
        opacity: 1,
        duration: 1.2,
        stagger: 0.2,
        ease: "power3.out",
        onComplete: () => {
            // Animate underlines
            gsap.to('.word::after', {
                scaleX: 1,
                duration: 0.8,
                stagger: 0.1,
                ease: "power2.out"
            });
        }
    }, "-=0.3");
    
    // Hero subtitle
    tl.fromTo('.subtitle-line', {
        y: 30,
        opacity: 0
    }, {
        y: 0,
        opacity: 1,
        duration: 0.8,
        stagger: 0.1,
        ease: "power2.out"
    }, "-=0.3");
    
    // Hero buttons
    tl.fromTo('.hero-actions .btn', {
        y: 30,
        opacity: 0,
        scale: 0.9
    }, {
        y: 0,
        opacity: 1,
        scale: 1,
        duration: 0.6,
        stagger: 0.1,
        ease: "back.out(1.7)"
    }, "-=0.2");
    
    // Hero scroll indicator
    tl.fromTo('.hero-scroll', {
        y: 20,
        opacity: 0
    }, {
        y: 0,
        opacity: 1,
        duration: 0.6,
        ease: "power2.out"
    }, "-=0.2");
}

// ===== SCROLL TRIGGERED ANIMATIONS =====
function initScrollAnimations() {
    // Section titles
    gsap.utils.toArray('.section-title').forEach(title => {
        const words = title.querySelectorAll('.title-word');
        
        gsap.fromTo(words, {
            y: 50,
            opacity: 0
        }, {
            y: 0,
            opacity: 1,
            duration: 0.8,
            stagger: 0.1,
            ease: "power2.out",
            scrollTrigger: {
                trigger: title,
                start: "top 80%",
                end: "bottom 20%",
                toggleActions: "play none none reverse"
            }
        });
    });

    // Feature items
    gsap.utils.toArray('.feature-item').forEach(item => {
        gsap.fromTo(item, {
            y: 50,
            opacity: 0,
            scale: 0.9
        }, {
            y: 0,
            opacity: 1,
            scale: 1,
            duration: 0.8,
            ease: "back.out(1.7)",
            scrollTrigger: {
                trigger: item,
                start: "top 85%",
                end: "bottom 15%",
                toggleActions: "play none none reverse"
            }
        });
    });

    // Service cards
    gsap.utils.toArray('.service-card').forEach((card, index) => {
        gsap.fromTo(card, {
            x: -50,
            opacity: 0
        }, {
            x: 0,
            opacity: 1,
            duration: 0.8,
            delay: index * 0.1,
            ease: "power2.out",
            scrollTrigger: {
                trigger: card,
                start: "top 85%",
                end: "bottom 15%",
                toggleActions: "play none none reverse"
            }
        });
    });

    // Work items
    gsap.utils.toArray('.work-item').forEach(item => {
        gsap.fromTo(item, {
            y: 50,
            opacity: 0,
            scale: 0.95
        }, {
            y: 0,
            opacity: 1,
            scale: 1,
            duration: 0.8,
            ease: "power2.out",
            scrollTrigger: {
                trigger: item,
                start: "top 85%",
                end: "bottom 15%",
                toggleActions: "play none none reverse"
            }
        });
    });

    // Stats counter animation
    gsap.utils.toArray('.stat-number').forEach(stat => {
        const target = parseInt(stat.getAttribute('data-count'));
        
        ScrollTrigger.create({
            trigger: stat,
            start: "top 80%",
            onEnter: () => {
                gsap.to(stat, {
                    textContent: target,
                    duration: 2,
                    ease: "power2.out",
                    snap: { textContent: 1 },
                    onUpdate: function() {
                        stat.textContent = Math.ceil(stat.textContent);
                    }
                });
            }
        });
    });
}

// ===== INTERACTIVE ELEMENTS =====
function initInteractiveElements() {
    // Button hover effects
    document.querySelectorAll('.btn').forEach(btn => {
        btn.addEventListener('mouseenter', () => {
            gsap.to(btn, {
                scale: 1.05,
                y: -2,
                duration: 0.3,
                ease: "power2.out"
            });
        });

        btn.addEventListener('mouseleave', () => {
            gsap.to(btn, {
                scale: 1,
                y: 0,
                duration: 0.3,
                ease: "power2.out"
            });
        });
    });

    // Service card interactions
    document.querySelectorAll('.service-card').forEach(card => {
        card.addEventListener('mouseenter', () => {
            gsap.to(card, {
                scale: 1.02,
                y: -5,
                duration: 0.3,
                ease: "power2.out"
            });
        });

        card.addEventListener('mouseleave', () => {
            gsap.to(card, {
                scale: 1,
                y: 0,
                duration: 0.3,
                ease: "power2.out"
            });
        });
    });

    // Work item interactions
    document.querySelectorAll('.work-item').forEach(item => {
        item.addEventListener('mouseenter', () => {
            gsap.to(item, {
                scale: 1.03,
                y: -8,
                duration: 0.4,
                ease: "power2.out"
            });
        });

        item.addEventListener('mouseleave', () => {
            gsap.to(item, {
                scale: 1,
                y: 0,
                duration: 0.4,
                ease: "power2.out"
            });
        });
    });
}

// ===== FLOATING ELEMENTS =====
function createFloatingElements() {
    const container = document.getElementById('floating-elements');
    if (!container) return;

    // Create floating dots
    for (let i = 0; i < 15; i++) {
        const dot = document.createElement('div');
        dot.className = 'floating-dot';
        dot.style.left = Math.random() * 100 + '%';
        dot.style.animationDelay = Math.random() * 15 + 's';
        dot.style.animationDuration = (15 + Math.random() * 10) + 's';
        container.appendChild(dot);
    }
}

// ===== INITIALIZATION =====
document.addEventListener('DOMContentLoaded', () => {
    // Start with loader
    document.body.style.overflow = 'hidden';
    initLoader();
    createFloatingElements();

    // Refresh ScrollTrigger after everything loads
    window.addEventListener('load', () => {
        ScrollTrigger.refresh();
    });
});

// ===== RESIZE HANDLER =====
window.addEventListener('resize', () => {
    isDesktop = window.innerWidth > 768;
    ScrollTrigger.refresh();
});
