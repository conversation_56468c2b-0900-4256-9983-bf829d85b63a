// ===== GSAP REGISTRATION =====
gsap.registerPlugin(ScrollTrigger, TextPlugin);

// ===== GLOBAL VARIABLES =====
let cursor = document.getElementById('cursor');
let cursorFollower = document.getElementById('cursor-follower');
let isDesktop = window.innerWidth > 768;

// ===== CURSOR ANIMATION =====
if (isDesktop) {
    document.addEventListener('mousemove', (e) => {
        gsap.to(cursor, {
            x: e.clientX,
            y: e.clientY,
            duration: 0.1,
            ease: "power2.out"
        });
        
        gsap.to(cursorFollower, {
            x: e.clientX,
            y: e.clientY,
            duration: 0.3,
            ease: "power2.out"
        });
    });

    // Cursor hover effects
    const hoverElements = document.querySelectorAll('a, button, .service-card, .work-item');
    hoverElements.forEach(element => {
        element.addEventListener('mouseenter', () => {
            gsap.to(cursor, { scale: 1.5, duration: 0.3 });
            gsap.to(cursorFollower, { scale: 1.5, duration: 0.3 });
        });
        
        element.addEventListener('mouseleave', () => {
            gsap.to(cursor, { scale: 1, duration: 0.3 });
            gsap.to(cursorFollower, { scale: 1, duration: 0.3 });
        });
    });
}

// ===== MORPHING TEXT ANIMATION =====
function initMorphingText() {
    const morphingWord = document.querySelector('.morphing-word');
    const chars = morphingWord.querySelectorAll('.char');
    
    // Initial animation
    gsap.fromTo(chars, {
        y: 100,
        opacity: 0,
        rotation: 15
    }, {
        y: 0,
        opacity: 1,
        rotation: 0,
        duration: 1,
        stagger: 0.1,
        ease: "back.out(1.7)",
        delay: 0.5
    });

    // Continuous morphing animation
    const words = ['Anything', 'Websites', 'Brands', 'Dreams', 'Magic', 'Future'];
    let currentIndex = 0;

    setInterval(() => {
        currentIndex = (currentIndex + 1) % words.length;
        const newWord = words[currentIndex];
        
        // Animate out current chars
        gsap.to(chars, {
            y: -50,
            opacity: 0,
            rotation: -15,
            duration: 0.5,
            stagger: 0.05,
            ease: "power2.in",
            onComplete: () => {
                // Update text content
                newWord.split('').forEach((char, index) => {
                    if (chars[index]) {
                        chars[index].textContent = char;
                    }
                });
                
                // Animate in new chars
                gsap.fromTo(chars, {
                    y: 50,
                    opacity: 0,
                    rotation: 15
                }, {
                    y: 0,
                    opacity: 1,
                    rotation: 0,
                    duration: 0.5,
                    stagger: 0.05,
                    ease: "back.out(1.7)"
                });
            }
        });
    }, 3000);
}

// ===== HERO ANIMATIONS =====
function initHeroAnimations() {
    const tl = gsap.timeline();
    
    // Hero badge
    tl.fromTo('.hero-badge', {
        y: 30,
        opacity: 0,
        scale: 0.8
    }, {
        y: 0,
        opacity: 1,
        scale: 1,
        duration: 0.8,
        ease: "back.out(1.7)"
    });
    
    // Hero title words
    tl.fromTo('.title-line .word', {
        y: 100,
        opacity: 0
    }, {
        y: 0,
        opacity: 1,
        duration: 1,
        stagger: 0.2,
        ease: "power3.out"
    }, "-=0.5");
    
    // Hero subtitle
    tl.fromTo('.subtitle-line', {
        y: 30,
        opacity: 0
    }, {
        y: 0,
        opacity: 1,
        duration: 0.8,
        stagger: 0.1,
        ease: "power2.out"
    }, "-=0.3");
    
    // Hero buttons
    tl.fromTo('.hero-actions .btn', {
        y: 30,
        opacity: 0,
        scale: 0.9
    }, {
        y: 0,
        opacity: 1,
        scale: 1,
        duration: 0.6,
        stagger: 0.1,
        ease: "back.out(1.7)"
    }, "-=0.2");
    
    // Hero scroll indicator
    tl.fromTo('.hero-scroll', {
        y: 20,
        opacity: 0
    }, {
        y: 0,
        opacity: 1,
        duration: 0.6,
        ease: "power2.out"
    }, "-=0.2");
}

// ===== SCROLL TRIGGERED ANIMATIONS =====
function initScrollAnimations() {
    // Section titles
    gsap.utils.toArray('.section-title').forEach(title => {
        const words = title.querySelectorAll('.title-word');
        
        gsap.fromTo(words, {
            y: 50,
            opacity: 0
        }, {
            y: 0,
            opacity: 1,
            duration: 0.8,
            stagger: 0.1,
            ease: "power2.out",
            scrollTrigger: {
                trigger: title,
                start: "top 80%",
                end: "bottom 20%",
                toggleActions: "play none none reverse"
            }
        });
    });

    // Feature items
    gsap.utils.toArray('.feature-item').forEach(item => {
        gsap.fromTo(item, {
            y: 50,
            opacity: 0,
            scale: 0.9
        }, {
            y: 0,
            opacity: 1,
            scale: 1,
            duration: 0.8,
            ease: "back.out(1.7)",
            scrollTrigger: {
                trigger: item,
                start: "top 85%",
                end: "bottom 15%",
                toggleActions: "play none none reverse"
            }
        });
    });

    // Service cards
    gsap.utils.toArray('.service-card').forEach((card, index) => {
        gsap.fromTo(card, {
            x: -50,
            opacity: 0
        }, {
            x: 0,
            opacity: 1,
            duration: 0.8,
            delay: index * 0.1,
            ease: "power2.out",
            scrollTrigger: {
                trigger: card,
                start: "top 85%",
                end: "bottom 15%",
                toggleActions: "play none none reverse"
            }
        });
    });

    // Work items
    gsap.utils.toArray('.work-item').forEach(item => {
        gsap.fromTo(item, {
            y: 50,
            opacity: 0,
            scale: 0.95
        }, {
            y: 0,
            opacity: 1,
            scale: 1,
            duration: 0.8,
            ease: "power2.out",
            scrollTrigger: {
                trigger: item,
                start: "top 85%",
                end: "bottom 15%",
                toggleActions: "play none none reverse"
            }
        });
    });

    // Stats counter animation
    gsap.utils.toArray('.stat-number').forEach(stat => {
        const target = parseInt(stat.getAttribute('data-count'));
        
        ScrollTrigger.create({
            trigger: stat,
            start: "top 80%",
            onEnter: () => {
                gsap.to(stat, {
                    textContent: target,
                    duration: 2,
                    ease: "power2.out",
                    snap: { textContent: 1 },
                    onUpdate: function() {
                        stat.textContent = Math.ceil(stat.textContent);
                    }
                });
            }
        });
    });
}

// ===== INITIALIZATION =====
document.addEventListener('DOMContentLoaded', () => {
    // Initialize all animations
    initHeroAnimations();
    initMorphingText();
    initScrollAnimations();
    
    // Refresh ScrollTrigger after everything loads
    window.addEventListener('load', () => {
        ScrollTrigger.refresh();
    });
});

// ===== RESIZE HANDLER =====
window.addEventListener('resize', () => {
    isDesktop = window.innerWidth > 768;
    ScrollTrigger.refresh();
});
