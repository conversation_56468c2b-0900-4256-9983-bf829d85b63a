// ===== REVOLUTIONARY WEBGL BACKGROUND SYSTEM =====

class WebGLBackground {
    constructor() {
        this.canvas = document.getElementById('webgl-canvas');
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.geometry = null;
        this.material = null;
        this.mesh = null;
        this.uniforms = {};
        this.mouse = { x: 0, y: 0 };
        this.time = 0;
        
        if (this.canvas && window.THREE) {
            this.init();
        }
    }

    init() {
        this.setupScene();
        this.createShaders();
        this.createGeometry();
        this.setupEventListeners();
        this.animate();
    }

    setupScene() {
        // Scene
        this.scene = new THREE.Scene();

        // Camera
        this.camera = new THREE.OrthographicCamera(-1, 1, 1, -1, 0, 1);

        // Renderer
        this.renderer = new THREE.WebGLRenderer({
            canvas: this.canvas,
            alpha: true,
            antialias: true
        });
        this.renderer.setSize(window.innerWidth, window.innerHeight);
        this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
    }

    createShaders() {
        // Vertex Shader
        const vertexShader = `
            varying vec2 vUv;
            void main() {
                vUv = uv;
                gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
            }
        `;

        // Fragment Shader - Revolutionary gradient with dynamic effects
        const fragmentShader = `
            uniform float u_time;
            uniform vec2 u_resolution;
            uniform vec2 u_mouse;
            varying vec2 vUv;

            // Noise function
            float random(vec2 st) {
                return fract(sin(dot(st.xy, vec2(12.9898,78.233))) * 43758.5453123);
            }

            float noise(vec2 st) {
                vec2 i = floor(st);
                vec2 f = fract(st);
                float a = random(i);
                float b = random(i + vec2(1.0, 0.0));
                float c = random(i + vec2(0.0, 1.0));
                float d = random(i + vec2(1.0, 1.0));
                vec2 u = f * f * (3.0 - 2.0 * f);
                return mix(a, b, u.x) + (c - a)* u.y * (1.0 - u.x) + (d - b) * u.x * u.y;
            }

            // Fractal Brownian Motion
            float fbm(vec2 st) {
                float value = 0.0;
                float amplitude = 0.5;
                float frequency = 0.0;
                for (int i = 0; i < 5; i++) {
                    value += amplitude * noise(st);
                    st *= 2.0;
                    amplitude *= 0.5;
                }
                return value;
            }

            void main() {
                vec2 st = vUv;
                vec2 mouse = u_mouse / u_resolution;
                
                // Create dynamic gradient base
                vec3 color1 = vec3(1.0, 0.76, 0.13); // #ffc321
                vec3 color2 = vec3(0.4, 0.43, 0.96); // #6366f1
                vec3 color3 = vec3(0.93, 0.29, 0.6); // #ec4899
                
                // Time-based movement
                float t = u_time * 0.5;
                
                // Create flowing gradient
                float gradient1 = sin(st.x * 3.0 + t) * 0.5 + 0.5;
                float gradient2 = cos(st.y * 2.0 + t * 0.7) * 0.5 + 0.5;
                float gradient3 = sin((st.x + st.y) * 1.5 + t * 0.3) * 0.5 + 0.5;
                
                // Mouse interaction
                float mouseInfluence = 1.0 - length(st - mouse) * 2.0;
                mouseInfluence = max(0.0, mouseInfluence);
                
                // Noise overlay
                float noiseValue = fbm(st * 3.0 + t * 0.1);
                
                // Combine colors
                vec3 finalColor = mix(color1, color2, gradient1);
                finalColor = mix(finalColor, color3, gradient2 * 0.7);
                
                // Add mouse interaction
                finalColor += mouseInfluence * 0.3 * color1;
                
                // Add noise texture
                finalColor += noiseValue * 0.1;
                
                // Create subtle animation
                float wave = sin(st.x * 10.0 + t * 2.0) * sin(st.y * 8.0 + t * 1.5) * 0.05;
                finalColor += wave;
                
                // Fade edges
                float vignette = 1.0 - length(st - 0.5) * 1.2;
                vignette = smoothstep(0.0, 1.0, vignette);
                
                // Apply vignette and opacity
                finalColor *= vignette;
                float alpha = 0.15 + mouseInfluence * 0.1;
                
                gl_FragColor = vec4(finalColor, alpha);
            }
        `;

        // Create uniforms
        this.uniforms = {
            u_time: { value: 0.0 },
            u_resolution: { value: new THREE.Vector2(window.innerWidth, window.innerHeight) },
            u_mouse: { value: new THREE.Vector2(0.0, 0.0) }
        };

        // Create material
        this.material = new THREE.ShaderMaterial({
            uniforms: this.uniforms,
            vertexShader: vertexShader,
            fragmentShader: fragmentShader,
            transparent: true,
            blending: THREE.AdditiveBlending
        });
    }

    createGeometry() {
        // Create plane geometry
        this.geometry = new THREE.PlaneGeometry(2, 2);
        this.mesh = new THREE.Mesh(this.geometry, this.material);
        this.scene.add(this.mesh);
    }

    setupEventListeners() {
        // Mouse movement
        window.addEventListener('mousemove', (event) => {
            this.mouse.x = event.clientX;
            this.mouse.y = window.innerHeight - event.clientY; // Flip Y coordinate
            
            this.uniforms.u_mouse.value.x = this.mouse.x;
            this.uniforms.u_mouse.value.y = this.mouse.y;
        });

        // Window resize
        window.addEventListener('resize', () => {
            this.handleResize();
        });

        // Touch events for mobile
        window.addEventListener('touchmove', (event) => {
            if (event.touches.length > 0) {
                const touch = event.touches[0];
                this.mouse.x = touch.clientX;
                this.mouse.y = window.innerHeight - touch.clientY;
                
                this.uniforms.u_mouse.value.x = this.mouse.x;
                this.uniforms.u_mouse.value.y = this.mouse.y;
            }
        });
    }

    animate() {
        this.time += 0.01;
        this.uniforms.u_time.value = this.time;
        
        // Render
        this.renderer.render(this.scene, this.camera);
        
        // Continue animation
        requestAnimationFrame(() => this.animate());
    }

    handleResize() {
        const width = window.innerWidth;
        const height = window.innerHeight;
        
        // Update renderer
        this.renderer.setSize(width, height);
        this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
        
        // Update uniforms
        this.uniforms.u_resolution.value.x = width;
        this.uniforms.u_resolution.value.y = height;
        
        // Update canvas
        this.canvas.style.width = width + 'px';
        this.canvas.style.height = height + 'px';
    }

    // Public methods for external control
    setIntensity(intensity) {
        if (this.material) {
            this.material.opacity = intensity;
        }
    }

    setColors(color1, color2, color3) {
        // This would require updating the shader uniforms
        // Implementation depends on specific color change requirements
    }

    pause() {
        this.isPaused = true;
    }

    resume() {
        this.isPaused = false;
        if (!this.animationId) {
            this.animate();
        }
    }

    destroy() {
        // Clean up resources
        if (this.geometry) this.geometry.dispose();
        if (this.material) this.material.dispose();
        if (this.renderer) this.renderer.dispose();
        
        // Remove event listeners
        window.removeEventListener('mousemove', this.handleMouseMove);
        window.removeEventListener('resize', this.handleResize);
        window.removeEventListener('touchmove', this.handleTouchMove);
    }
}

// Advanced WebGL Effects Class
class WebGLEffects {
    constructor(webglBackground) {
        this.webgl = webglBackground;
        this.effects = [];
    }

    addRippleEffect(x, y, intensity = 1.0) {
        const effect = {
            x: x,
            y: y,
            intensity: intensity,
            time: 0,
            duration: 2.0
        };
        
        this.effects.push(effect);
        
        // Remove effect after duration
        setTimeout(() => {
            const index = this.effects.indexOf(effect);
            if (index > -1) {
                this.effects.splice(index, 1);
            }
        }, effect.duration * 1000);
    }

    addColorWave(color, duration = 3.0) {
        // Implementation for color wave effect
        // This would modify the shader uniforms to create a color wave
    }

    update() {
        // Update all active effects
        this.effects.forEach(effect => {
            effect.time += 0.016; // Assuming 60fps
        });
    }
}

// Performance Monitor
class WebGLPerformanceMonitor {
    constructor() {
        this.fps = 0;
        this.frameCount = 0;
        this.lastTime = performance.now();
        this.isLowPerformance = false;
    }

    update() {
        this.frameCount++;
        const currentTime = performance.now();
        
        if (currentTime - this.lastTime >= 1000) {
            this.fps = this.frameCount;
            this.frameCount = 0;
            this.lastTime = currentTime;
            
            // Check for low performance
            if (this.fps < 30) {
                this.isLowPerformance = true;
                this.handleLowPerformance();
            } else {
                this.isLowPerformance = false;
            }
        }
    }

    handleLowPerformance() {
        // Reduce quality for better performance
        console.log('Low performance detected, reducing WebGL quality');
        
        // Could disable certain effects or reduce resolution
        const canvas = document.getElementById('webgl-canvas');
        if (canvas) {
            canvas.style.opacity = '0.5';
        }
    }
}

// Initialize WebGL Background
window.addEventListener('DOMContentLoaded', () => {
    // Only initialize if WebGL is supported
    if (window.THREE && WebGLRenderingContext) {
        try {
            window.WebGLBackground = WebGLBackground;
            window.webglBackground = new WebGLBackground();
            window.webglEffects = new WebGLEffects(window.webglBackground);
            window.webglPerformanceMonitor = new WebGLPerformanceMonitor();
            
            console.log('WebGL Background initialized successfully');
        } catch (error) {
            console.warn('WebGL initialization failed:', error);
            // Fallback to CSS background
            document.body.classList.add('webgl-fallback');
        }
    } else {
        console.warn('WebGL not supported, using CSS fallback');
        document.body.classList.add('webgl-fallback');
    }
});

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { WebGLBackground, WebGLEffects, WebGLPerformanceMonitor };
}
