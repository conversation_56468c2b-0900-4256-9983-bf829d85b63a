# Personal Portfolio Website

A modern, responsive portfolio website built with HTML, CSS, and JavaScript, featuring smooth GSAP animations and a clean, professional design.

## Features

- **Modern Design**: Clean, minimalist design following the latest web standards
- **Responsive Layout**: Fully responsive design that works on all devices
- **GSAP Animations**: Smooth, professional animations using GSAP library
- **Interactive Portfolio**: Filterable portfolio section with hover effects
- **Contact Form**: Functional contact form with validation
- **Performance Optimized**: Fast loading with optimized code and assets
- **Accessibility**: Built with accessibility best practices

## Sections

1. **Hero Section**: Eye-catching introduction with animated elements
2. **About Section**: Personal information, skills, and statistics
3. **Portfolio Section**: Showcase of projects with filtering capabilities
4. **Contact Section**: Contact information and contact form

## Technologies Used

- HTML5 (Semantic markup)
- CSS3 (Grid, Flexbox, Custom Properties)
- JavaScript (ES6+)
- GSAP (GreenSock Animation Platform)
- ScrollTrigger (GSAP plugin)

## Getting Started

1. Clone or download the repository
2. Open `index.html` in your web browser
3. Customize the content with your personal information
4. Replace placeholder images with your actual project images
5. Update contact information and social links

## Customization

### Personal Information
- Update the name, role, and description in the hero section
- Modify the about section with your background and skills
- Add your actual contact information

### Portfolio Projects
- Replace the sample portfolio data in `js/script.js`
- Add your actual project images to an `images` folder
- Update project descriptions and technologies used

### Styling
- Modify CSS custom properties in `:root` to change colors and fonts
- Adjust spacing and layout in the CSS file
- Add your brand colors and typography

### Contact Form
- Integrate with a backend service or form handler
- Update the form submission logic in `js/script.js`
- Add form validation as needed

## Browser Support

- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)

## Performance

The website is optimized for performance with:
- Efficient CSS and JavaScript
- Optimized animations
- Lazy loading for images
- Minimal external dependencies

## License

This project is open source and available under the [MIT License](LICENSE).

## Credits

- GSAP for animations
- Google Fonts for typography
- Placeholder images from placeholder.com

---

Feel free to customize this portfolio template to match your personal brand and showcase your unique work!
