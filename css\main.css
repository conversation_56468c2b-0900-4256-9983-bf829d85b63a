/* ===== REVOLUTIONARY CSS ARCHITECTURE ===== */

/* Reset & Base */
*,
*::before,
*::after {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Revolutionary Color System */
    --primary: #ffc321;
    --primary-dark: #e6b01e;
    --primary-light: #fff4d6;
    --secondary: #0a0a0a;
    --accent: #6366f1;
    --accent-pink: #ec4899;
    --accent-cyan: #06b6d4;
    --text-primary: #0a0a0a;
    --text-secondary: #4a5568;
    --text-muted: #718096;
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-glass: rgba(255, 255, 255, 0.1);
    --border: rgba(255, 255, 255, 0.2);
    
    /* Advanced Gradients */
    --gradient-primary: linear-gradient(135deg, #ffc321 0%, #ff8a00 100%);
    --gradient-secondary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-accent: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --gradient-cyber: linear-gradient(135deg, #00f5ff 0%, #ff00ff 50%, #ffff00 100%);
    --gradient-glass: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    
    /* Typography System */
    --font-display: 'NewBlackTypeface', 'Inter', sans-serif;
    --font-body: 'Inter', sans-serif;
    --font-mono: 'JetBrains Mono', monospace;
    
    /* Fluid Typography */
    --text-xs: clamp(0.75rem, 0.7rem + 0.25vw, 0.875rem);
    --text-sm: clamp(0.875rem, 0.8rem + 0.375vw, 1rem);
    --text-base: clamp(1rem, 0.9rem + 0.5vw, 1.125rem);
    --text-lg: clamp(1.125rem, 1rem + 0.625vw, 1.25rem);
    --text-xl: clamp(1.25rem, 1.1rem + 0.75vw, 1.5rem);
    --text-2xl: clamp(1.5rem, 1.3rem + 1vw, 2rem);
    --text-3xl: clamp(1.875rem, 1.6rem + 1.375vw, 2.5rem);
    --text-4xl: clamp(2.25rem, 1.9rem + 1.75vw, 3rem);
    --text-5xl: clamp(3rem, 2.5rem + 2.5vw, 4rem);
    --text-6xl: clamp(3.75rem, 3rem + 3.75vw, 5rem);
    --text-7xl: clamp(4.5rem, 3.5rem + 5vw, 6rem);
    --text-8xl: clamp(6rem, 4.5rem + 7.5vw, 8rem);
    
    /* Spacing System */
    --space-1: 0.25rem;
    --space-2: 0.5rem;
    --space-3: 0.75rem;
    --space-4: 1rem;
    --space-5: 1.25rem;
    --space-6: 1.5rem;
    --space-8: 2rem;
    --space-10: 2.5rem;
    --space-12: 3rem;
    --space-16: 4rem;
    --space-20: 5rem;
    --space-24: 6rem;
    --space-32: 8rem;
    --space-40: 10rem;
    --space-48: 12rem;
    --space-64: 16rem;
    
    /* Advanced Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    --shadow-glow: 0 0 20px rgba(255, 195, 33, 0.3);
    --shadow-cyber: 0 0 30px rgba(0, 245, 255, 0.5);
    
    /* Advanced Borders */
    --radius-sm: 0.125rem;
    --radius: 0.25rem;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --radius-xl: 0.75rem;
    --radius-2xl: 1rem;
    --radius-3xl: 1.5rem;
    --radius-full: 9999px;
    
    /* Transitions */
    --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-base: 300ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: 500ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-bounce: 600ms cubic-bezier(0.68, -0.55, 0.265, 1.55);
    
    /* Z-Index System */
    --z-hide: -1;
    --z-base: 0;
    --z-docked: 10;
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-banner: 1030;
    --z-overlay: 1040;
    --z-modal: 1050;
    --z-popover: 1060;
    --z-tooltip: 1070;
    --z-toast: 1080;
    --z-loader: 9999;
}

/* Base Styles */
html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: var(--font-body);
    font-weight: 400;
    line-height: 1.6;
    color: var(--text-primary);
    background: var(--bg-primary);
    overflow-x: hidden;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--space-6);
}

/* ===== ADVANCED LOADING SYSTEM ===== */
.loader-system {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--bg-primary);
    z-index: var(--z-loader);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: opacity 0.8s ease, visibility 0.8s ease;
}

.loader-system.hidden {
    opacity: 0;
    visibility: hidden;
    pointer-events: none;
}

.loader-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

#loader-canvas {
    width: 100%;
    height: 100%;
    opacity: 0.3;
}

.loader-content {
    position: relative;
    z-index: 2;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--space-8);
}

.loader-logo {
    width: 120px;
    height: 120px;
    position: relative;
}

.logo-svg {
    width: 100%;
    height: 100%;
    filter: drop-shadow(0 0 20px rgba(255, 195, 33, 0.5));
}

.logo-path {
    stroke-dasharray: 200;
    stroke-dashoffset: 200;
    animation: drawLogo 2s ease-in-out infinite alternate;
}

.loader-text {
    display: flex;
    flex-direction: column;
    gap: var(--space-2);
}

.loader-word {
    font-family: var(--font-display);
    font-size: var(--text-3xl);
    font-weight: 900;
    color: var(--text-primary);
    position: relative;
    overflow: hidden;
}

.loader-word::before {
    content: attr(data-text);
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    transform: translateX(-100%);
    animation: slideText 2s ease-in-out infinite;
}

.loader-progress {
    width: 300px;
    display: flex;
    flex-direction: column;
    gap: var(--space-4);
}

.progress-container {
    position: relative;
    width: 100%;
    height: 6px;
    background: rgba(255, 195, 33, 0.1);
    border-radius: var(--radius-full);
    overflow: hidden;
}

.progress-bar {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 0%;
    background: var(--gradient-primary);
    border-radius: var(--radius-full);
    transition: width 0.3s ease;
}

.progress-glow {
    position: absolute;
    top: -2px;
    left: 0;
    height: calc(100% + 4px);
    width: 20px;
    background: radial-gradient(circle, rgba(255, 195, 33, 0.8) 0%, transparent 70%);
    border-radius: var(--radius-full);
    transform: translateX(-100%);
    animation: progressGlow 2s ease-in-out infinite;
}

.progress-text {
    display: flex;
    justify-content: center;
    align-items: baseline;
    gap: var(--space-1);
    font-family: var(--font-mono);
    font-weight: 600;
    color: var(--text-secondary);
}

.progress-number {
    font-size: var(--text-2xl);
    color: var(--primary);
}

.progress-percent {
    font-size: var(--text-lg);
}

.loader-particles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
}

/* ===== WEBGL CANVAS ===== */
.webgl-canvas {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: var(--z-hide);
    pointer-events: none;
}

/* ===== PARTICLE SYSTEM ===== */
.particle-system {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: var(--z-hide);
    pointer-events: none;
}

/* ===== NAVIGATION ===== */
.nav {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: var(--z-sticky);
    padding: var(--space-4) 0;
    backdrop-filter: blur(20px);
    background: rgba(255, 255, 255, 0.9);
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    transition: all var(--transition-base);
}

.nav.scrolled {
    padding: var(--space-3) 0;
    box-shadow: var(--shadow-lg);
}

.nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--space-6);
}

.nav-logo .logo {
    height: 50px;
    width: auto;
    transition: var(--transition-base);
}

.nav-logo .logo:hover {
    transform: scale(1.05);
}

.nav-menu {
    display: flex;
    gap: var(--space-8);
    align-items: center;
}

.nav-link {
    position: relative;
    color: var(--text-primary);
    text-decoration: none;
    font-weight: 500;
    font-size: var(--text-base);
    padding: var(--space-2) 0;
    transition: var(--transition-base);
    overflow: hidden;
}

.nav-link::before {
    content: attr(data-text);
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    transform: translateY(100%);
    transition: transform var(--transition-base);
}

.nav-link:hover::before {
    transform: translateY(0);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--gradient-primary);
    transition: width var(--transition-base);
}

.nav-link:hover::after {
    width: 100%;
}

.nav-toggle {
    display: none;
    flex-direction: column;
    gap: var(--space-1);
    cursor: pointer;
    padding: var(--space-2);
}

.nav-toggle span {
    width: 25px;
    height: 3px;
    background: var(--text-primary);
    border-radius: var(--radius-full);
    transition: var(--transition-base);
}

/* ===== HERO SECTION ===== */
.hero {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    position: relative;
    overflow: hidden;
    text-align: center;
    padding-top: 80px;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: var(--z-hide);
}

.hero-shapes {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.shape {
    position: absolute;
    border-radius: 50%;
    filter: blur(100px);
    opacity: 0.1;
    animation: heroFloat 20s ease-in-out infinite;
}

.shape-1 {
    width: 400px;
    height: 400px;
    background: var(--gradient-primary);
    top: 10%;
    left: 10%;
    animation-delay: 0s;
}

.shape-2 {
    width: 300px;
    height: 300px;
    background: var(--gradient-secondary);
    top: 60%;
    right: 10%;
    animation-delay: 7s;
}

.shape-3 {
    width: 350px;
    height: 350px;
    background: var(--gradient-accent);
    bottom: 20%;
    left: 20%;
    animation-delay: 14s;
}

.hero-content {
    position: relative;
    z-index: 1;
    max-width: 1000px;
    margin: 0 auto;
    padding: 0 var(--space-6);
}

.hero-badge {
    position: relative;
    display: inline-block;
    padding: var(--space-3) var(--space-6);
    background: var(--bg-glass);
    backdrop-filter: blur(20px);
    border: 1px solid var(--border);
    border-radius: var(--radius-full);
    margin-bottom: var(--space-8);
    overflow: hidden;
}

.badge-text {
    font-size: var(--text-sm);
    font-weight: 600;
    color: var(--primary);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    position: relative;
    z-index: 2;
}

.badge-glow {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-primary);
    opacity: 0.1;
    border-radius: var(--radius-full);
    animation: badgeGlow 3s ease-in-out infinite;
}

.hero-title {
    font-family: var(--font-display);
    font-size: var(--text-8xl);
    font-weight: 900;
    line-height: 0.9;
    margin-bottom: var(--space-8);
    letter-spacing: -0.02em;
}

.title-line {
    display: block;
    overflow: hidden;
    margin-bottom: var(--space-2);
}

.word {
    display: inline-block;
    position: relative;
    margin-right: var(--space-4);
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.morphing-word {
    background: var(--gradient-cyber);
    background-size: 200% 200%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradientShift 4s ease-in-out infinite;
    position: relative;
}

.morphing-word::before {
    content: '';
    position: absolute;
    top: -10px;
    left: -10px;
    right: -10px;
    bottom: -10px;
    background: var(--gradient-cyber);
    opacity: 0.1;
    border-radius: var(--radius-lg);
    z-index: -1;
    animation: morphGlow 4s ease-in-out infinite;
}

.char {
    display: inline-block;
    transition: all var(--transition-base);
}

.hero-subtitle {
    font-size: var(--text-xl);
    color: var(--text-secondary);
    margin-bottom: var(--space-12);
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
    line-height: 1.7;
}

.subtitle-line {
    display: block;
    margin-bottom: var(--space-2);
}

.hero-actions {
    display: flex;
    gap: var(--space-6);
    justify-content: center;
    margin-bottom: var(--space-16);
    flex-wrap: wrap;
}

/* ===== ADVANCED BUTTON SYSTEM ===== */
.btn {
    position: relative;
    display: inline-flex;
    align-items: center;
    gap: var(--space-3);
    padding: var(--space-4) var(--space-8);
    border: none;
    border-radius: var(--radius-full);
    font-family: var(--font-body);
    font-size: var(--text-base);
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    overflow: hidden;
    transition: all var(--transition-base);
    backdrop-filter: blur(20px);
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.6s ease;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: var(--gradient-primary);
    color: white;
    box-shadow: var(--shadow-glow);
}

.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-xl), var(--shadow-glow);
}

.btn-secondary {
    background: var(--bg-glass);
    color: var(--text-primary);
    border: 1px solid var(--border);
}

.btn-secondary:hover {
    transform: translateY(-3px);
    background: rgba(255, 255, 255, 0.2);
    box-shadow: var(--shadow-lg);
}

.btn-large {
    padding: var(--space-5) var(--space-10);
    font-size: var(--text-lg);
}

.btn-text {
    position: relative;
    z-index: 2;
}

.btn-arrow {
    font-size: var(--text-xl);
    transition: transform var(--transition-base);
}

.btn:hover .btn-arrow {
    transform: translateX(4px);
}

.btn-ripple {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.6s ease, height 0.6s ease;
}

.btn:active .btn-ripple {
    width: 300px;
    height: 300px;
}

.hero-scroll {
    position: absolute;
    bottom: var(--space-8);
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--space-4);
    opacity: 0.7;
    transition: opacity var(--transition-base);
}

.hero-scroll:hover {
    opacity: 1;
}

.scroll-text {
    font-size: var(--text-sm);
    color: var(--text-secondary);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.1em;
}

.scroll-indicator {
    width: 2px;
    height: 50px;
    background: linear-gradient(to bottom, var(--primary), transparent);
    border-radius: var(--radius-full);
    position: relative;
    overflow: hidden;
}

.scroll-line {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 20px;
    background: var(--primary);
    border-radius: var(--radius-full);
    animation: scrollMove 2s ease-in-out infinite;
}

/* ===== SECTIONS ===== */
.about,
.services,
.cta {
    padding: var(--space-32) 0;
    position: relative;
}

.section-header {
    text-align: center;
    margin-bottom: var(--space-20);
}

.section-title {
    font-family: var(--font-display);
    font-size: var(--text-5xl);
    font-weight: 900;
    margin-bottom: var(--space-6);
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.section-subtitle {
    font-size: var(--text-xl);
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
}

/* ===== ABOUT SECTION ===== */
.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-16);
    align-items: center;
}

.about-text p {
    font-size: var(--text-lg);
    color: var(--text-secondary);
    line-height: 1.8;
}

.about-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--space-8);
}

.stat-item {
    text-align: center;
    padding: var(--space-6);
    background: var(--bg-glass);
    backdrop-filter: blur(20px);
    border: 1px solid var(--border);
    border-radius: var(--radius-2xl);
    transition: all var(--transition-base);
}

.stat-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.stat-number {
    display: block;
    font-family: var(--font-display);
    font-size: var(--text-4xl);
    font-weight: 900;
    color: var(--primary);
    margin-bottom: var(--space-2);
}

.stat-label {
    font-size: var(--text-sm);
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    font-weight: 600;
}

/* ===== SERVICES SECTION ===== */
.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--space-8);
}

.service-card {
    position: relative;
    padding: var(--space-8);
    background: var(--bg-glass);
    backdrop-filter: blur(20px);
    border: 1px solid var(--border);
    border-radius: var(--radius-2xl);
    transition: all var(--transition-base);
    overflow: hidden;
    cursor: pointer;
}

.service-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-primary);
    opacity: 0;
    transition: opacity var(--transition-base);
}

.service-card:hover::before {
    opacity: 0.05;
}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-2xl);
    border-color: var(--primary);
}

.service-icon {
    width: 60px;
    height: 60px;
    margin-bottom: var(--space-6);
    color: var(--primary);
    position: relative;
    z-index: 2;
}

.service-icon svg {
    width: 100%;
    height: 100%;
    stroke-width: 1.5;
}

.service-title {
    font-family: var(--font-display);
    font-size: var(--text-2xl);
    font-weight: 700;
    margin-bottom: var(--space-4);
    color: var(--text-primary);
    position: relative;
    z-index: 2;
}

.service-description {
    font-size: var(--text-base);
    color: var(--text-secondary);
    line-height: 1.7;
    margin-bottom: var(--space-6);
    position: relative;
    z-index: 2;
}

.service-link {
    display: inline-flex;
    align-items: center;
    gap: var(--space-2);
    color: var(--primary);
    text-decoration: none;
    font-weight: 600;
    font-size: var(--text-base);
    transition: all var(--transition-base);
    position: relative;
    z-index: 2;
}

.service-link:hover {
    transform: translateX(5px);
}

/* ===== CTA SECTION ===== */
.cta {
    background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-primary) 100%);
    text-align: center;
}

.cta-content {
    max-width: 800px;
    margin: 0 auto;
}

.cta-title {
    font-family: var(--font-display);
    font-size: var(--text-5xl);
    font-weight: 900;
    margin-bottom: var(--space-6);
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.cta-subtitle {
    font-size: var(--text-xl);
    color: var(--text-secondary);
    margin-bottom: var(--space-10);
}

/* ===== ADVANCED ANIMATIONS ===== */
@keyframes drawLogo {
    0% {
        stroke-dashoffset: 200;
    }
    100% {
        stroke-dashoffset: 0;
    }
}

@keyframes slideText {
    0% {
        transform: translateX(-100%);
    }
    50% {
        transform: translateX(0);
    }
    100% {
        transform: translateX(100%);
    }
}

@keyframes progressGlow {
    0%, 100% {
        transform: translateX(-100%);
    }
    50% {
        transform: translateX(400%);
    }
}

@keyframes badgeGlow {
    0%, 100% {
        opacity: 0.1;
        transform: scale(1);
    }
    50% {
        opacity: 0.2;
        transform: scale(1.05);
    }
}

@keyframes gradientShift {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

@keyframes morphGlow {
    0%, 100% {
        opacity: 0.1;
        transform: scale(1);
    }
    50% {
        opacity: 0.3;
        transform: scale(1.1);
    }
}

@keyframes heroFloat {
    0%, 100% {
        transform: translateY(0) rotate(0deg);
    }
    33% {
        transform: translateY(-30px) rotate(120deg);
    }
    66% {
        transform: translateY(15px) rotate(240deg);
    }
}

@keyframes scrollMove {
    0% {
        transform: translateY(-20px);
        opacity: 0;
    }
    50% {
        opacity: 1;
    }
    100% {
        transform: translateY(50px);
        opacity: 0;
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-10px);
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

@keyframes shimmer {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1024px) {
    .container {
        padding: 0 var(--space-4);
    }

    .nav-container {
        padding: 0 var(--space-4);
    }

    .hero-title {
        font-size: var(--text-6xl);
    }

    .about-content {
        grid-template-columns: 1fr;
        gap: var(--space-12);
    }

    .about-stats {
        grid-template-columns: repeat(3, 1fr);
        gap: var(--space-6);
    }

    .services-grid {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: var(--space-6);
    }
}

@media (max-width: 768px) {
    .nav-menu {
        display: none;
    }

    .nav-toggle {
        display: flex;
    }

    .hero {
        padding-top: 100px;
        min-height: 90vh;
    }

    .hero-title {
        font-size: var(--text-5xl);
    }

    .hero-subtitle {
        font-size: var(--text-lg);
    }

    .hero-actions {
        flex-direction: column;
        align-items: center;
        gap: var(--space-4);
    }

    .btn {
        width: 100%;
        max-width: 300px;
        justify-content: center;
    }

    .about-stats {
        grid-template-columns: 1fr;
        gap: var(--space-4);
    }

    .services-grid {
        grid-template-columns: 1fr;
        gap: var(--space-6);
    }

    .section-title {
        font-size: var(--text-4xl);
    }

    .cta-title {
        font-size: var(--text-4xl);
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: var(--text-4xl);
    }

    .hero-subtitle {
        font-size: var(--text-base);
    }

    .loader-word {
        font-size: var(--text-2xl);
    }

    .progress-container {
        width: 250px;
    }

    .section-title {
        font-size: var(--text-3xl);
    }

    .cta-title {
        font-size: var(--text-3xl);
    }
}

/* ===== UTILITY CLASSES ===== */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

.no-scroll {
    overflow: hidden;
}

.fade-in {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.8s ease;
}

.fade-in.visible {
    opacity: 1;
    transform: translateY(0);
}

.slide-up {
    opacity: 0;
    transform: translateY(50px);
    transition: all 0.6s ease;
}

.slide-up.visible {
    opacity: 1;
    transform: translateY(0);
}

.scale-in {
    opacity: 0;
    transform: scale(0.8);
    transition: all 0.5s ease;
}

.scale-in.visible {
    opacity: 1;
    transform: scale(1);
}

/* ===== PERFORMANCE OPTIMIZATIONS ===== */
.will-change-transform {
    will-change: transform;
}

.will-change-opacity {
    will-change: opacity;
}

.gpu-accelerated {
    transform: translateZ(0);
    backface-visibility: hidden;
    perspective: 1000px;
}

/* ===== ACCESSIBILITY ===== */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* ===== PRINT STYLES ===== */
@media print {
    .loader-system,
    .webgl-canvas,
    .particle-system,
    .nav,
    .hero-scroll {
        display: none !important;
    }

    body {
        background: white !important;
        color: black !important;
    }

    .hero {
        min-height: auto;
        padding: var(--space-8) 0;
    }
}
