/* ===== CONTACT PAGE SPECIFIC STYLES ===== */

/* Contact Hero Section */
.contact-hero {
  min-height: 100vh;
  display: flex;
  align-items: center;
  position: relative;
  padding-top: 70px;
  overflow: hidden;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.hero-gradient {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, 
    var(--accent-50) 0%, 
    var(--background) 30%, 
    var(--primary-50) 70%, 
    var(--accent-100) 100%);
  opacity: 0.8;
}

.hero-pattern {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: 
    radial-gradient(circle at 30% 70%, var(--accent-100) 2px, transparent 2px),
    radial-gradient(circle at 70% 30%, var(--primary-100) 1px, transparent 1px);
  background-size: 80px 80px, 50px 50px;
  opacity: 0.4;
  animation: patternFloat 25s linear infinite;
}

.floating-elements {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.floating-element {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--primary-200), var(--accent-200));
  opacity: 0.6;
  animation: floatUpDown 6s ease-in-out infinite;
}

.floating-element.element-1 {
  width: 60px;
  height: 60px;
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.floating-element.element-2 {
  width: 40px;
  height: 40px;
  top: 60%;
  right: 15%;
  animation-delay: 2s;
}

.floating-element.element-3 {
  width: 80px;
  height: 80px;
  bottom: 30%;
  left: 20%;
  animation-delay: 4s;
}

.floating-element.element-4 {
  width: 50px;
  height: 50px;
  top: 40%;
  right: 30%;
  animation-delay: 1s;
}

@keyframes floatUpDown {
  0%, 100% { transform: translateY(0) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}

.hero-content {
  position: relative;
  z-index: 2;
  text-align: center;
  max-width: 700px;
  margin: 0 auto;
}

.hero-features {
  display: flex;
  justify-content: center;
  gap: var(--space-8);
  margin-top: var(--space-12);
  flex-wrap: wrap;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-4);
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border-radius: var(--radius-xl);
  border: 1px solid var(--border-light);
  font-weight: 600;
  color: var(--text-primary);
}

.feature-icon {
  font-size: 1.25rem;
}

/* Contact Methods Section */
.contact-methods {
  padding: var(--space-24) 0;
  background: var(--background-secondary);
}

.methods-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-8);
}

.method-card {
  background: var(--background);
  border-radius: var(--radius-2xl);
  padding: var(--space-8);
  text-align: center;
  box-shadow: var(--shadow-lg);
  transition: var(--transition-medium);
  position: relative;
  overflow: hidden;
}

.method-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-500), var(--accent-500));
  transform: scaleX(0);
  transition: var(--transition-medium);
}

.method-card:hover::before {
  transform: scaleX(1);
}

.method-card:hover {
  transform: translateY(-10px);
  box-shadow: var(--shadow-xl);
}

.method-icon {
  margin-bottom: var(--space-6);
}

.icon-bg {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, var(--primary-100), var(--primary-200));
  border-radius: var(--radius-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  margin: 0 auto;
  transition: var(--transition-medium);
}

.method-card:hover .icon-bg {
  background: linear-gradient(135deg, var(--primary-500), var(--accent-500));
  transform: scale(1.1) rotate(5deg);
}

.method-title {
  font-family: var(--font-display);
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--space-3);
}

.method-description {
  color: var(--text-secondary);
  margin-bottom: var(--space-6);
  line-height: 1.6;
}

.method-link {
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
  color: var(--primary-600);
  font-weight: 600;
  text-decoration: none;
  transition: var(--transition-fast);
}

.method-link:hover {
  color: var(--primary-700);
  transform: translateX(5px);
}

.link-arrow {
  font-size: 1.25rem;
  transition: var(--transition-fast);
}

.method-link:hover .link-arrow {
  transform: translateX(3px);
}

.social-links {
  display: flex;
  justify-content: center;
  gap: var(--space-3);
}

.social-link {
  padding: var(--space-2) var(--space-4);
  background: var(--background-secondary);
  color: var(--text-secondary);
  text-decoration: none;
  border-radius: var(--radius-lg);
  font-weight: 500;
  transition: var(--transition-medium);
  border: 1px solid var(--border-light);
}

.social-link:hover {
  background: var(--primary-500);
  color: white;
  border-color: var(--primary-500);
  transform: translateY(-2px);
}

/* Contact Form Section */
.contact-form-section {
  padding: var(--space-24) 0;
}

.form-container {
  max-width: 800px;
  margin: 0 auto;
}

.form-header {
  text-align: center;
  margin-bottom: var(--space-16);
}

.form-title {
  font-family: var(--font-display);
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--space-4);
}

.form-subtitle {
  font-size: 1.125rem;
  color: var(--text-secondary);
  line-height: 1.6;
}

.advanced-contact-form {
  background: var(--background);
  border-radius: var(--radius-2xl);
  padding: var(--space-10);
  box-shadow: var(--shadow-xl);
  border: 1px solid var(--border-light);
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-6);
  margin-bottom: var(--space-6);
}

.form-group {
  position: relative;
  margin-bottom: var(--space-6);
}

.form-label {
  display: block;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-2);
  font-size: 0.95rem;
}

.form-input,
.form-select,
.form-textarea {
  width: 100%;
  padding: var(--space-4);
  border: 2px solid var(--border-color);
  border-radius: var(--radius-lg);
  font-size: 1rem;
  color: var(--text-primary);
  background: var(--background);
  transition: var(--transition-medium);
  position: relative;
  z-index: 2;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-textarea {
  resize: vertical;
  min-height: 120px;
  font-family: inherit;
}

.input-border {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, var(--primary-500), var(--accent-500));
  transition: var(--transition-medium);
  z-index: 1;
}

.form-input:focus + .input-border,
.form-select:focus + .input-border,
.form-textarea:focus + .input-border {
  width: 100%;
}

.form-actions {
  text-align: center;
  margin-top: var(--space-8);
}

.submit-btn {
  background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
  color: white;
  border: none;
  padding: var(--space-4) var(--space-10);
  border-radius: var(--radius-xl);
  font-size: 1.125rem;
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition-medium);
  position: relative;
  overflow: hidden;
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
  min-width: 200px;
  justify-content: center;
}

.submit-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
  transition: left 0.5s ease;
}

.submit-btn:hover::before {
  left: 100%;
}

.submit-btn:hover {
  background: linear-gradient(135deg, var(--primary-600), var(--accent-500));
  transform: translateY(-3px);
  box-shadow: var(--shadow-colored);
}

.btn-loading {
  display: none;
}

.submit-btn.loading .btn-text,
.submit-btn.loading .btn-icon {
  display: none;
}

.submit-btn.loading .btn-loading {
  display: block;
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255,255,255,0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* FAQ Section */
.faq-section {
  padding: var(--space-24) 0;
  background: var(--background-secondary);
}

.faq-grid {
  display: grid;
  gap: var(--space-4);
  margin-top: var(--space-12);
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.faq-item {
  background: var(--background);
  border-radius: var(--radius-xl);
  border: 1px solid var(--border-light);
  overflow: hidden;
  transition: var(--transition-medium);
}

.faq-item:hover {
  box-shadow: var(--shadow-md);
}

.faq-question {
  width: 100%;
  padding: var(--space-6);
  background: none;
  border: none;
  text-align: left;
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: var(--transition-fast);
}

.faq-question:hover {
  color: var(--primary-600);
}

.faq-icon {
  font-size: 1.25rem;
  transition: var(--transition-fast);
}

.faq-item.active .faq-icon {
  transform: rotate(45deg);
}

.faq-answer {
  padding: 0 var(--space-6) var(--space-6);
  color: var(--text-secondary);
  line-height: 1.6;
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;
}

.faq-item.active .faq-answer {
  max-height: 200px;
}

/* Success Modal */
.success-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  display: none;
  align-items: center;
  justify-content: center;
}

.modal-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(5px);
}

.modal-content {
  background: var(--background);
  border-radius: var(--radius-2xl);
  padding: var(--space-10);
  text-align: center;
  max-width: 400px;
  margin: var(--space-4);
  position: relative;
  z-index: 2;
  box-shadow: var(--shadow-2xl);
}

.success-icon {
  font-size: 4rem;
  margin-bottom: var(--space-4);
}

.success-title {
  font-family: var(--font-display);
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--space-3);
}

.success-message {
  color: var(--text-secondary);
  margin-bottom: var(--space-6);
  line-height: 1.6;
}

.modal-close-btn {
  background: var(--primary-500);
  color: white;
  border: none;
  padding: var(--space-3) var(--space-6);
  border-radius: var(--radius-lg);
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition-medium);
}

.modal-close-btn:hover {
  background: var(--primary-600);
  transform: translateY(-2px);
}

/* Animation Classes */
.fade-in-up {
  opacity: 0;
  transform: translateY(30px);
}

.fade-in-left {
  opacity: 0;
  transform: translateX(-30px);
}

.fade-in-right {
  opacity: 0;
  transform: translateX(30px);
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero-features {
    gap: var(--space-4);
  }

  .feature-item {
    padding: var(--space-2) var(--space-3);
    font-size: 0.875rem;
  }

  .methods-grid {
    grid-template-columns: 1fr;
    gap: var(--space-6);
  }

  .form-row {
    grid-template-columns: 1fr;
    gap: var(--space-4);
  }

  .advanced-contact-form {
    padding: var(--space-6);
  }

  .form-title {
    font-size: 2rem;
  }

  .submit-btn {
    width: 100%;
    padding: var(--space-4) var(--space-6);
  }

  .floating-element {
    display: none;
  }
}

@media (max-width: 480px) {
  .hero-features {
    flex-direction: column;
    align-items: center;
    gap: var(--space-3);
  }

  .feature-item {
    width: 100%;
    max-width: 250px;
    justify-content: center;
  }

  .method-card {
    padding: var(--space-6);
  }

  .advanced-contact-form {
    padding: var(--space-4);
  }

  .modal-content {
    margin: var(--space-2);
    padding: var(--space-6);
  }
}
