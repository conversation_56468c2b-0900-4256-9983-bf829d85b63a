// ===== PORTFOLIO PAGE SPECIFIC JAVASCRIPT =====

// Portfolio data with enhanced details
const portfolioData = [
    {
        id: 1,
        title: 'E-commerce Revolution',
        category: 'web',
        description: 'A cutting-edge e-commerce platform with AI-powered recommendations and seamless checkout experience.',
        image: 'https://via.placeholder.com/500x300/3b82f6/ffffff?text=E-commerce+Platform',
        tags: ['React', 'Node.js', 'MongoDB', 'Stripe', 'AI/ML'],
        featured: true,
        year: '2024',
        client: 'TechCorp Inc.'
    },
    {
        id: 2,
        title: 'FinTech Mobile App',
        category: 'app',
        description: 'Secure mobile banking application with biometric authentication and real-time transactions.',
        image: 'https://via.placeholder.com/500x300/eab308/ffffff?text=Banking+App',
        tags: ['React Native', 'Firebase', 'Biometrics', 'Security'],
        featured: true,
        year: '2024',
        client: 'SecureBank'
    },
    {
        id: 3,
        title: 'Brand Identity System',
        category: 'branding',
        description: 'Complete brand identity redesign for a sustainable fashion startup, including logo and guidelines.',
        image: 'https://via.placeholder.com/500x300/10b981/ffffff?text=Brand+Identity',
        tags: ['Illustrator', 'Photoshop', 'Brand Strategy', 'Print Design'],
        featured: false,
        year: '2023',
        client: 'EcoFashion'
    },
    {
        id: 4,
        title: 'SaaS Dashboard',
        category: 'ui',
        description: 'Intuitive dashboard design for a project management SaaS with advanced data visualization.',
        image: 'https://via.placeholder.com/500x300/8b5cf6/ffffff?text=SaaS+Dashboard',
        tags: ['Figma', 'Vue.js', 'D3.js', 'UX Research'],
        featured: true,
        year: '2024',
        client: 'ProjectFlow'
    },
    {
        id: 5,
        title: 'Food Delivery App',
        category: 'app',
        description: 'Modern food delivery application with real-time tracking and social features.',
        image: 'https://via.placeholder.com/500x300/ef4444/ffffff?text=Food+Delivery',
        tags: ['Flutter', 'Firebase', 'Maps API', 'Real-time'],
        featured: false,
        year: '2023',
        client: 'QuickEats'
    },
    {
        id: 6,
        title: 'Creative Agency Website',
        category: 'web',
        description: 'Award-winning website for a creative agency featuring stunning animations and portfolio showcase.',
        image: 'https://via.placeholder.com/500x300/06b6d4/ffffff?text=Agency+Website',
        tags: ['GSAP', 'Three.js', 'WebGL', 'CSS Animations'],
        featured: false,
        year: '2023',
        client: 'CreativeStudio'
    },
    {
        id: 7,
        title: 'Healthcare Platform',
        category: 'ui',
        description: 'Comprehensive healthcare platform design focusing on accessibility and user experience.',
        image: 'https://via.placeholder.com/500x300/f59e0b/ffffff?text=Healthcare+UI',
        tags: ['Accessibility', 'User Research', 'Prototyping', 'Testing'],
        featured: true,
        year: '2024',
        client: 'HealthTech'
    },
    {
        id: 8,
        title: 'Luxury Hotel Branding',
        category: 'branding',
        description: 'Elegant branding solution for a luxury hotel chain with sophisticated visual identity.',
        image: 'https://via.placeholder.com/500x300/7c3aed/ffffff?text=Hotel+Branding',
        tags: ['Luxury Design', 'Print Collateral', 'Photography', 'Packaging'],
        featured: false,
        year: '2023',
        client: 'LuxuryStay'
    }
];

// Case studies data
const caseStudiesData = [
    {
        id: 1,
        title: 'E-commerce Revolution',
        subtitle: 'Increasing conversion rates by 150%',
        description: 'A comprehensive redesign of an e-commerce platform that resulted in significant improvements in user engagement and sales conversion.',
        icon: '🚀',
        link: '#'
    },
    {
        id: 2,
        title: 'FinTech Mobile Experience',
        subtitle: 'Simplifying complex financial operations',
        description: 'Designing an intuitive mobile banking experience that makes complex financial operations accessible to everyday users.',
        icon: '💳',
        link: '#'
    },
    {
        id: 3,
        title: 'SaaS Dashboard Optimization',
        subtitle: 'Reducing user onboarding time by 60%',
        description: 'Streamlining the user experience of a complex SaaS platform through strategic UX improvements and data visualization.',
        icon: '📊',
        link: '#'
    },
    {
        id: 4,
        title: 'Brand Identity Transformation',
        subtitle: 'Building trust through visual storytelling',
        description: 'Complete brand transformation for a sustainable fashion startup, creating a cohesive identity that resonates with eco-conscious consumers.',
        icon: '🎨',
        link: '#'
    }
];

// Initialize portfolio page
document.addEventListener('DOMContentLoaded', function() {
    initPortfolioPage();
});

function initPortfolioPage() {
    // Initialize enhanced loader
    initEnhancedLoader();
    
    // Initialize portfolio animations
    initPortfolioAnimations();
    
    // Render portfolio items
    renderPortfolioItems(portfolioData);
    
    // Render case studies
    renderCaseStudies(caseStudiesData);
    
    // Initialize portfolio filtering
    initPortfolioFiltering();
    
    // Initialize scroll animations
    initScrollAnimations();
}

// Enhanced loader for portfolio page
function initEnhancedLoader() {
    const loader = document.querySelector('.loader-overlay');
    const progressFill = document.querySelector('.progress-bar-fill');
    const progressGlow = document.querySelector('.progress-bar-glow');
    const progressPercentage = document.querySelector('.progress-percentage');
    const loadingMessages = document.querySelectorAll('.loading-message');
    
    let progress = 0;
    let messageIndex = 0;
    
    const progressInterval = setInterval(() => {
        progress += Math.random() * 15 + 5;
        
        if (progress >= 100) {
            progress = 100;
            clearInterval(progressInterval);
            
            // Complete loading
            setTimeout(() => {
                gsap.to(loader, {
                    opacity: 0,
                    duration: 1,
                    ease: 'power2.inOut',
                    onComplete: () => {
                        loader.style.display = 'none';
                        startPortfolioAnimations();
                    }
                });
            }, 500);
        }
        
        // Update progress bar
        progressFill.style.width = progress + '%';
        progressGlow.style.width = progress + '%';
        progressPercentage.textContent = Math.round(progress) + '%';
        
        // Update loading messages
        if (progress > 25 && messageIndex === 0) {
            updateLoadingMessage(1);
            messageIndex = 1;
        } else if (progress > 50 && messageIndex === 1) {
            updateLoadingMessage(2);
            messageIndex = 2;
        } else if (progress > 75 && messageIndex === 2) {
            updateLoadingMessage(3);
            messageIndex = 3;
        } else if (progress > 90 && messageIndex === 3) {
            updateLoadingMessage(4);
            messageIndex = 4;
        }
    }, 100);
    
    function updateLoadingMessage(index) {
        loadingMessages.forEach(msg => msg.classList.remove('active'));
        if (loadingMessages[index]) {
            loadingMessages[index].classList.add('active');
        }
    }
}

// Portfolio animations
function initPortfolioAnimations() {
    // Set initial states
    gsap.set('.fade-in', { opacity: 0, y: 30 });
    gsap.set('.fade-in-up', { opacity: 0, y: 50 });
    gsap.set('.fade-in-left', { opacity: 0, x: -50 });
    gsap.set('.fade-in-right', { opacity: 0, x: 50 });
}

function startPortfolioAnimations() {
    // Hero section animations
    const heroTimeline = gsap.timeline();
    
    heroTimeline
        .to('.hero-badge', { opacity: 1, y: 0, duration: 0.8, ease: 'power2.out' })
        .to('.title-line', { 
            opacity: 1, 
            y: 0, 
            duration: 0.8, 
            stagger: 0.2, 
            ease: 'power2.out' 
        }, '-=0.4')
        .to('.hero-description', { opacity: 1, y: 0, duration: 0.8, ease: 'power2.out' }, '-=0.4')
        .to('.hero-stats .stat-item', { 
            opacity: 1, 
            y: 0, 
            duration: 0.6, 
            stagger: 0.1, 
            ease: 'back.out(1.7)' 
        }, '-=0.2');
}

// Render portfolio items
function renderPortfolioItems(data) {
    const portfolioGrid = document.getElementById('portfolioGrid');
    portfolioGrid.innerHTML = '';
    
    data.forEach((item, index) => {
        const portfolioItem = createPortfolioItem(item);
        portfolioGrid.appendChild(portfolioItem);
        
        // Add staggered animation
        gsap.from(portfolioItem, {
            opacity: 0,
            y: 50,
            duration: 0.8,
            delay: index * 0.1,
            ease: 'power2.out',
            scrollTrigger: {
                trigger: portfolioItem,
                start: 'top 85%',
                toggleActions: 'play none none reverse'
            }
        });
    });
}

// Create portfolio item element
function createPortfolioItem(item) {
    const portfolioItem = document.createElement('div');
    portfolioItem.className = `portfolio-item ${item.category}`;
    portfolioItem.innerHTML = `
        <div class="portfolio-image">
            <img src="${item.image}" alt="${item.title}" loading="lazy">
            <div class="portfolio-overlay">
                <div class="portfolio-overlay-content">
                    <h4 class="overlay-title">View Project</h4>
                    <p class="overlay-subtitle">Click to explore</p>
                </div>
            </div>
        </div>
        <div class="portfolio-content">
            <div class="portfolio-category">${item.category.toUpperCase()}</div>
            <h3 class="portfolio-title">${item.title}</h3>
            <p class="portfolio-description">${item.description}</p>
            <div class="portfolio-tags">
                ${item.tags.map(tag => `<span class="portfolio-tag">${tag}</span>`).join('')}
            </div>
        </div>
    `;
    
    // Add click event
    portfolioItem.addEventListener('click', () => {
        openProjectModal(item);
    });
    
    return portfolioItem;
}

// Render case studies
function renderCaseStudies(data) {
    const caseStudiesGrid = document.querySelector('.case-studies-grid');
    caseStudiesGrid.innerHTML = '';
    
    data.forEach((study, index) => {
        const caseStudyItem = createCaseStudyItem(study);
        caseStudiesGrid.appendChild(caseStudyItem);
        
        // Add animation
        gsap.from(caseStudyItem, {
            opacity: 0,
            y: 30,
            duration: 0.8,
            delay: index * 0.2,
            ease: 'power2.out',
            scrollTrigger: {
                trigger: caseStudyItem,
                start: 'top 85%',
                toggleActions: 'play none none reverse'
            }
        });
    });
}

// Create case study item
function createCaseStudyItem(study) {
    const caseStudyItem = document.createElement('div');
    caseStudyItem.className = 'case-study-item';
    caseStudyItem.innerHTML = `
        <div class="case-study-header">
            <div class="case-study-icon">${study.icon}</div>
            <div class="case-study-meta">
                <h3>${study.title}</h3>
                <p>${study.subtitle}</p>
            </div>
        </div>
        <p class="case-study-description">${study.description}</p>
        <a href="${study.link}" class="case-study-link">
            Read Case Study
            <span>→</span>
        </a>
    `;
    
    return caseStudyItem;
}

// Portfolio filtering
function initPortfolioFiltering() {
    const filterButtons = document.querySelectorAll('.filter-btn');
    
    filterButtons.forEach(button => {
        button.addEventListener('click', () => {
            // Update active button
            filterButtons.forEach(btn => btn.classList.remove('active'));
            button.classList.add('active');
            
            // Filter portfolio items
            const filter = button.getAttribute('data-filter');
            filterPortfolioItems(filter);
        });
    });
}

// Filter portfolio items with animation
function filterPortfolioItems(filter) {
    const portfolioItems = document.querySelectorAll('.portfolio-item');
    
    // Animate out all items
    gsap.to(portfolioItems, {
        opacity: 0,
        scale: 0.8,
        duration: 0.3,
        stagger: 0.05,
        ease: 'power2.inOut',
        onComplete: () => {
            // Show/hide items based on filter
            portfolioItems.forEach(item => {
                if (filter === 'all' || item.classList.contains(filter)) {
                    item.style.display = 'block';
                } else {
                    item.style.display = 'none';
                }
            });
            
            // Animate in visible items
            const visibleItems = document.querySelectorAll('.portfolio-item[style*="block"], .portfolio-item:not([style*="none"])');
            gsap.fromTo(visibleItems, 
                { opacity: 0, scale: 0.8 },
                { 
                    opacity: 1, 
                    scale: 1, 
                    duration: 0.5, 
                    stagger: 0.1, 
                    ease: 'back.out(1.7)' 
                }
            );
        }
    });
}

// Scroll animations
function initScrollAnimations() {
    // Fade in animations
    gsap.utils.toArray('.fade-in').forEach(element => {
        gsap.to(element, {
            opacity: 1,
            y: 0,
            duration: 0.8,
            ease: 'power2.out',
            scrollTrigger: {
                trigger: element,
                start: 'top 80%',
                toggleActions: 'play none none reverse'
            }
        });
    });
    
    // Fade in up animations
    gsap.utils.toArray('.fade-in-up').forEach(element => {
        gsap.to(element, {
            opacity: 1,
            y: 0,
            duration: 0.8,
            ease: 'power2.out',
            scrollTrigger: {
                trigger: element,
                start: 'top 80%',
                toggleActions: 'play none none reverse'
            }
        });
    });
    
    // Fade in left animations
    gsap.utils.toArray('.fade-in-left').forEach(element => {
        gsap.to(element, {
            opacity: 1,
            x: 0,
            duration: 0.8,
            ease: 'power2.out',
            scrollTrigger: {
                trigger: element,
                start: 'top 80%',
                toggleActions: 'play none none reverse'
            }
        });
    });
    
    // Fade in right animations
    gsap.utils.toArray('.fade-in-right').forEach(element => {
        gsap.to(element, {
            opacity: 1,
            x: 0,
            duration: 0.8,
            ease: 'power2.out',
            scrollTrigger: {
                trigger: element,
                start: 'top 80%',
                toggleActions: 'play none none reverse'
            }
        });
    });
}

// Project modal (placeholder)
function openProjectModal(project) {
    console.log('Opening project modal for:', project);
    // Implement modal functionality here
    alert(`Opening project: ${project.title}\n\nThis would open a detailed project view with case study, images, and more information.`);
}
