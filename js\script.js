// ===== GLOBAL VARIABLES =====
let isLoading = true;

// ===== INITIALIZE APPLICATION =====
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

function initializeApp() {
    initLoader();
    initNavigation();
    initAnimations();
    initWorkSection();
    initContactForm();
    initSmoothScrolling();
}

// ===== LOADING SCREEN =====
function initLoader() {
    const loader = document.querySelector('.loader-overlay');
    const progressLine = document.querySelector('.progress-line');
    
    if (!loader) return;
    
    // Simulate loading progress
    setTimeout(() => {
        gsap.to(loader, {
            opacity: 0,
            duration: 0.8,
            ease: 'power2.inOut',
            onComplete: () => {
                loader.style.display = 'none';
                isLoading = false;
                startMainAnimations();
            }
        });
    }, 2500);
}

// ===== NAVIGATION =====
function initNavigation() {
    const navbar = document.querySelector('.navbar');
    const hamburger = document.querySelector('.hamburger');
    const navMenu = document.querySelector('.nav-menu');
    const navLinks = document.querySelectorAll('.nav-link');
    
    // Navbar scroll effect
    window.addEventListener('scroll', () => {
        if (window.scrollY > 100) {
            navbar.style.background = 'rgba(255, 255, 255, 0.95)';
            navbar.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.1)';
        } else {
            navbar.style.background = 'rgba(255, 255, 255, 0.8)';
            navbar.style.boxShadow = 'none';
        }
    });
    
    // Mobile menu toggle
    if (hamburger && navMenu) {
        hamburger.addEventListener('click', () => {
            hamburger.classList.toggle('active');
            navMenu.classList.toggle('active');
        });
    }
    
    // Smooth scroll for navigation links
    navLinks.forEach(link => {
        link.addEventListener('click', (e) => {
            const href = link.getAttribute('href');
            if (href.startsWith('#')) {
                e.preventDefault();
                const target = document.querySelector(href);
                if (target) {
                    const offsetTop = target.offsetTop - 80;
                    window.scrollTo({
                        top: offsetTop,
                        behavior: 'smooth'
                    });
                }
            }
        });
    });
}

// ===== ANIMATIONS =====
function initAnimations() {
    // Register GSAP plugins
    gsap.registerPlugin(ScrollTrigger);
    
    // Set initial states
    gsap.set('.hero-content > *', { opacity: 0, y: 50 });
    gsap.set('.service-card', { opacity: 0, y: 30 });
    gsap.set('.work-item', { opacity: 0, y: 30 });
    gsap.set('.feature-item', { opacity: 0, x: -30 });
    gsap.set('.visual-card', { opacity: 0, scale: 0.8 });
}

function startMainAnimations() {
    // Hero animations
    const heroTl = gsap.timeline();
    
    heroTl
        .to('.hero-badge', { opacity: 1, y: 0, duration: 0.8, ease: 'power2.out' })
        .to('.hero-title .title-line', { 
            opacity: 1, 
            y: 0, 
            duration: 0.8, 
            stagger: 0.2, 
            ease: 'power2.out' 
        }, '-=0.4')
        .to('.hero-description', { opacity: 1, y: 0, duration: 0.8, ease: 'power2.out' }, '-=0.4')
        .to('.hero-stats', { opacity: 1, y: 0, duration: 0.8, ease: 'power2.out' }, '-=0.4')
        .to('.hero-actions', { opacity: 1, y: 0, duration: 0.8, ease: 'power2.out' }, '-=0.4')
        .to('.scroll-indicator', { opacity: 1, y: 0, duration: 0.8, ease: 'power2.out' }, '-=0.4');
    
    // Services section animation
    gsap.to('.service-card', {
        opacity: 1,
        y: 0,
        duration: 0.8,
        stagger: 0.2,
        ease: 'power2.out',
        scrollTrigger: {
            trigger: '.services-grid',
            start: 'top 80%',
            end: 'bottom 20%',
            toggleActions: 'play none none reverse'
        }
    });
    
    // Work section animation
    gsap.to('.work-item', {
        opacity: 1,
        y: 0,
        duration: 0.8,
        stagger: 0.2,
        ease: 'power2.out',
        scrollTrigger: {
            trigger: '.work-grid',
            start: 'top 80%',
            end: 'bottom 20%',
            toggleActions: 'play none none reverse'
        }
    });
    
    // About section animations
    gsap.to('.feature-item', {
        opacity: 1,
        x: 0,
        duration: 0.8,
        stagger: 0.2,
        ease: 'power2.out',
        scrollTrigger: {
            trigger: '.about-features',
            start: 'top 80%',
            end: 'bottom 20%',
            toggleActions: 'play none none reverse'
        }
    });
    
    gsap.to('.visual-card', {
        opacity: 1,
        scale: 1,
        duration: 1,
        ease: 'back.out(1.7)',
        scrollTrigger: {
            trigger: '.about-visual',
            start: 'top 80%',
            end: 'bottom 20%',
            toggleActions: 'play none none reverse'
        }
    });
    
    // Parallax effect for gradient orbs
    gsap.to('.orb-1', {
        y: -100,
        rotation: 180,
        scrollTrigger: {
            trigger: '.hero',
            start: 'top top',
            end: 'bottom top',
            scrub: 1
        }
    });
    
    gsap.to('.orb-2', {
        y: -150,
        rotation: -180,
        scrollTrigger: {
            trigger: '.hero',
            start: 'top top',
            end: 'bottom top',
            scrub: 1
        }
    });
    
    gsap.to('.orb-3', {
        y: -80,
        rotation: 90,
        scrollTrigger: {
            trigger: '.hero',
            start: 'top top',
            end: 'bottom top',
            scrub: 1
        }
    });
}

// ===== WORK SECTION =====
function initWorkSection() {
    const workGrid = document.querySelector('.work-grid');
    
    if (!workGrid) return;
    
    // Sample work data
    const workData = [
        {
            title: 'E-commerce Platform',
            description: 'Modern e-commerce solution with advanced features and seamless user experience.',
            tags: ['Web Design', 'Development', 'E-commerce'],
            image: 'project1.jpg'
        },
        {
            title: 'Brand Identity Design',
            description: 'Complete brand identity package including logo, colors, and visual guidelines.',
            tags: ['Graphic Design', 'Branding', 'Logo'],
            image: 'project2.jpg'
        },
        {
            title: 'Wedding Website',
            description: 'Beautiful wedding website with RSVP system and photo gallery.',
            tags: ['Wedding Design', 'Web Development', 'RSVP'],
            image: 'project3.jpg'
        }
    ];
    
    // Generate work items
    workData.forEach(work => {
        const workItem = createWorkItem(work);
        workGrid.appendChild(workItem);
    });
}

function createWorkItem(work) {
    const workItem = document.createElement('div');
    workItem.className = 'work-item';
    
    workItem.innerHTML = `
        <div class="work-image" style="background: linear-gradient(135deg, #ffc321 0%, #ffd454 100%);">
            <!-- Placeholder for project image -->
        </div>
        <div class="work-content">
            <h3 class="work-title">${work.title}</h3>
            <p class="work-description">${work.description}</p>
            <div class="work-tags">
                ${work.tags.map(tag => `<span class="work-tag">${tag}</span>`).join('')}
            </div>
        </div>
    `;
    
    return workItem;
}

// ===== CONTACT FORM =====
function initContactForm() {
    const contactForm = document.getElementById('contactForm');
    
    if (!contactForm) return;
    
    contactForm.addEventListener('submit', handleFormSubmit);
    
    // Add input animations
    const formInputs = contactForm.querySelectorAll('input, select, textarea');
    formInputs.forEach(input => {
        input.addEventListener('focus', () => {
            gsap.to(input, { scale: 1.02, duration: 0.2, ease: 'power2.out' });
        });
        
        input.addEventListener('blur', () => {
            gsap.to(input, { scale: 1, duration: 0.2, ease: 'power2.out' });
        });
    });
}

function handleFormSubmit(e) {
    e.preventDefault();
    
    const submitBtn = e.target.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    
    // Show loading state
    submitBtn.innerHTML = '<span class="btn-text">Sending...</span>';
    submitBtn.disabled = true;
    
    // Simulate form submission
    setTimeout(() => {
        submitBtn.innerHTML = '<span class="btn-text">Message Sent!</span> <span class="btn-icon">✓</span>';
        
        setTimeout(() => {
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
            e.target.reset();
        }, 2000);
    }, 1500);
}

// ===== SMOOTH SCROLLING =====
function initSmoothScrolling() {
    // Add smooth scrolling to all anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                const offsetTop = target.offsetTop - 80;
                window.scrollTo({
                    top: offsetTop,
                    behavior: 'smooth'
                });
            }
        });
    });
}

// ===== UTILITY FUNCTIONS =====
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// ===== PERFORMANCE OPTIMIZATIONS =====
// Optimize scroll events
const optimizedScrollHandler = debounce(() => {
    // Handle scroll events here if needed
}, 16);

window.addEventListener('scroll', optimizedScrollHandler);

// Preload critical resources
function preloadResources() {
    const criticalImages = [
        'images/logo-black.png',
        'images/favicon.png'
    ];
    
    criticalImages.forEach(src => {
        const img = new Image();
        img.src = src;
    });
}

// Initialize preloading
preloadResources();
