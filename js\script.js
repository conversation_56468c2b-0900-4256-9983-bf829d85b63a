// ===== GSAP REGISTRATION =====
gsap.registerPlugin(ScrollTrigger);

// ===== GLOBAL VARIABLES =====
let isLoading = true;

// ===== DOM CONTENT LOADED =====
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

// ===== INITIALIZE APPLICATION =====
function initializeApp() {
    // Initialize loading screen
    initLoader();
    
    // Initialize navigation
    initNavigation();
    
    // Initialize GSAP animations
    initAnimations();
    
    // Initialize portfolio
    initPortfolio();
    
    // Initialize contact form
    initContactForm();
    
    // Initialize smooth scrolling
    initSmoothScrolling();
}

// ===== LOADING SCREEN =====
function initLoader() {
    const loader = document.querySelector('.loader');
    const loaderBar = document.querySelector('.loader-bar');
    
    // Animate loader bar
    gsap.to(loaderBar, {
        width: '100%',
        duration: 2,
        ease: 'power2.inOut',
        onComplete: () => {
            // Hide loader after animation
            gsap.to(loader, {
                opacity: 0,
                duration: 0.5,
                onComplete: () => {
                    loader.style.display = 'none';
                    isLoading = false;
                    // Start main animations
                    startMainAnimations();
                }
            });
        }
    });
}

// ===== NAVIGATION =====
function initNavigation() {
    const hamburger = document.querySelector('.hamburger');
    const navMenu = document.querySelector('.nav-menu');
    const navLinks = document.querySelectorAll('.nav-link');
    
    // Mobile menu toggle
    hamburger.addEventListener('click', () => {
        hamburger.classList.toggle('active');
        navMenu.classList.toggle('active');
    });
    
    // Close mobile menu when clicking on a link
    navLinks.forEach(link => {
        link.addEventListener('click', () => {
            hamburger.classList.remove('active');
            navMenu.classList.remove('active');
        });
    });
    
    // Active navigation highlighting
    window.addEventListener('scroll', () => {
        let current = '';
        const sections = document.querySelectorAll('section');
        
        sections.forEach(section => {
            const sectionTop = section.offsetTop;
            const sectionHeight = section.clientHeight;
            if (scrollY >= (sectionTop - 200)) {
                current = section.getAttribute('id');
            }
        });
        
        navLinks.forEach(link => {
            link.classList.remove('active');
            if (link.getAttribute('href') === `#${current}`) {
                link.classList.add('active');
            }
        });
    });
    
    // Navbar background on scroll
    window.addEventListener('scroll', () => {
        const navbar = document.querySelector('.navbar');
        if (window.scrollY > 100) {
            navbar.style.backgroundColor = 'rgba(255, 255, 255, 0.98)';
        } else {
            navbar.style.backgroundColor = 'rgba(255, 255, 255, 0.95)';
        }
    });
}

// ===== GSAP ANIMATIONS =====
function initAnimations() {
    // Set initial states for animations
    gsap.set('.fade-in', { opacity: 0, y: 30 });
    gsap.set('.fade-in-left', { opacity: 0, x: -30 });
    gsap.set('.fade-in-right', { opacity: 0, x: 30 });
    gsap.set('.scale-in', { opacity: 0, scale: 0.8 });
}

function startMainAnimations() {
    // Hero section animations
    const heroTimeline = gsap.timeline();
    
    heroTimeline
        .from('.hero-greeting', { opacity: 0, y: 20, duration: 0.6 })
        .from('.hero-name', { opacity: 0, y: 30, duration: 0.8 }, '-=0.3')
        .from('.hero-role', { opacity: 0, y: 20, duration: 0.6 }, '-=0.4')
        .from('.hero-description', { opacity: 0, y: 20, duration: 0.6 }, '-=0.2')
        .from('.hero-buttons .btn', { opacity: 0, y: 20, duration: 0.5, stagger: 0.1 }, '-=0.2')
        .from('.hero-shape', { opacity: 0, scale: 0.8, rotation: -10, duration: 1, ease: 'back.out(1.7)' }, '-=0.8')
        .from('.scroll-indicator', { opacity: 0, y: 20, duration: 0.5 }, '-=0.3');
    
    // Section animations with ScrollTrigger
    gsap.utils.toArray('.fade-in').forEach(element => {
        gsap.to(element, {
            opacity: 1,
            y: 0,
            duration: 0.8,
            ease: 'power2.out',
            scrollTrigger: {
                trigger: element,
                start: 'top 80%',
                end: 'bottom 20%',
                toggleActions: 'play none none reverse'
            }
        });
    });
    
    gsap.utils.toArray('.fade-in-left').forEach(element => {
        gsap.to(element, {
            opacity: 1,
            x: 0,
            duration: 0.8,
            ease: 'power2.out',
            scrollTrigger: {
                trigger: element,
                start: 'top 80%',
                toggleActions: 'play none none reverse'
            }
        });
    });
    
    gsap.utils.toArray('.fade-in-right').forEach(element => {
        gsap.to(element, {
            opacity: 1,
            x: 0,
            duration: 0.8,
            ease: 'power2.out',
            scrollTrigger: {
                trigger: element,
                start: 'top 80%',
                toggleActions: 'play none none reverse'
            }
        });
    });
    
    gsap.utils.toArray('.scale-in').forEach(element => {
        gsap.to(element, {
            opacity: 1,
            scale: 1,
            duration: 0.8,
            ease: 'back.out(1.7)',
            scrollTrigger: {
                trigger: element,
                start: 'top 80%',
                toggleActions: 'play none none reverse'
            }
        });
    });
    
    // Stats counter animation
    gsap.utils.toArray('.stat-number').forEach(stat => {
        const finalValue = stat.textContent;
        const numericValue = parseInt(finalValue.replace(/\D/g, ''));
        
        gsap.fromTo(stat, 
            { textContent: 0 },
            {
                textContent: numericValue,
                duration: 2,
                ease: 'power2.out',
                snap: { textContent: 1 },
                scrollTrigger: {
                    trigger: stat,
                    start: 'top 80%',
                    toggleActions: 'play none none reverse'
                },
                onUpdate: function() {
                    const currentValue = Math.round(this.targets()[0].textContent);
                    if (finalValue.includes('+')) {
                        stat.textContent = currentValue + '+';
                    } else if (finalValue.includes('%')) {
                        stat.textContent = currentValue + '%';
                    } else {
                        stat.textContent = currentValue;
                    }
                }
            }
        );
    });
    
    // Skills animation
    gsap.utils.toArray('.skill-item').forEach((skill, index) => {
        gsap.from(skill, {
            opacity: 0,
            y: 20,
            duration: 0.5,
            delay: index * 0.1,
            scrollTrigger: {
                trigger: '.skills-grid',
                start: 'top 80%',
                toggleActions: 'play none none reverse'
            }
        });
    });
}

// ===== PORTFOLIO =====
function initPortfolio() {
    // Sample portfolio data
    const portfolioData = [
        {
            id: 1,
            title: 'E-commerce Website',
            category: 'web',
            description: 'Modern e-commerce platform with responsive design and smooth animations.',
            image: 'https://via.placeholder.com/400x250/2563eb/ffffff?text=E-commerce+Site',
            tags: ['React', 'Node.js', 'MongoDB']
        },
        {
            id: 2,
            title: 'Mobile Banking App',
            category: 'app',
            description: 'Secure and user-friendly mobile banking application with modern UI.',
            image: 'https://via.placeholder.com/400x250/f59e0b/ffffff?text=Banking+App',
            tags: ['React Native', 'Firebase', 'UI/UX']
        },
        {
            id: 3,
            title: 'Brand Identity Design',
            category: 'branding',
            description: 'Complete brand identity package including logo, colors, and guidelines.',
            image: 'https://via.placeholder.com/400x250/10b981/ffffff?text=Brand+Identity',
            tags: ['Illustrator', 'Photoshop', 'Branding']
        },
        {
            id: 4,
            title: 'Portfolio Website',
            category: 'web',
            description: 'Creative portfolio website with GSAP animations and modern design.',
            image: 'https://via.placeholder.com/400x250/8b5cf6/ffffff?text=Portfolio+Site',
            tags: ['HTML', 'CSS', 'GSAP']
        },
        {
            id: 5,
            title: 'Task Management App',
            category: 'app',
            description: 'Productivity app for managing tasks and projects with team collaboration.',
            image: 'https://via.placeholder.com/400x250/ef4444/ffffff?text=Task+Manager',
            tags: ['Vue.js', 'Express', 'PostgreSQL']
        },
        {
            id: 6,
            title: 'Restaurant Branding',
            category: 'branding',
            description: 'Complete branding solution for a modern restaurant chain.',
            image: 'https://via.placeholder.com/400x250/06b6d4/ffffff?text=Restaurant+Brand',
            tags: ['Branding', 'Print Design', 'Photography']
        }
    ];
    
    // Render portfolio items
    renderPortfolio(portfolioData);
    
    // Portfolio filtering
    const filterButtons = document.querySelectorAll('.filter-btn');
    filterButtons.forEach(button => {
        button.addEventListener('click', () => {
            // Update active button
            filterButtons.forEach(btn => btn.classList.remove('active'));
            button.classList.add('active');
            
            // Filter portfolio items
            const filter = button.getAttribute('data-filter');
            filterPortfolio(portfolioData, filter);
        });
    });
}

function renderPortfolio(data) {
    const portfolioGrid = document.querySelector('.portfolio-grid');
    portfolioGrid.innerHTML = '';
    
    data.forEach((item, index) => {
        const portfolioItem = createPortfolioItem(item);
        portfolioGrid.appendChild(portfolioItem);
        
        // Add animation
        gsap.from(portfolioItem, {
            opacity: 0,
            y: 30,
            duration: 0.6,
            delay: index * 0.1,
            scrollTrigger: {
                trigger: portfolioItem,
                start: 'top 90%',
                toggleActions: 'play none none reverse'
            }
        });
    });
}

function createPortfolioItem(item) {
    const portfolioItem = document.createElement('div');
    portfolioItem.className = `portfolio-item ${item.category}`;
    portfolioItem.innerHTML = `
        <div class="portfolio-image">
            <img src="${item.image}" alt="${item.title}">
            <div class="portfolio-overlay">
                <div class="portfolio-overlay-content">
                    <h4>View Project</h4>
                    <p>Click to see details</p>
                </div>
            </div>
        </div>
        <div class="portfolio-content">
            <div class="portfolio-category">${item.category.toUpperCase()}</div>
            <h3 class="portfolio-title">${item.title}</h3>
            <p class="portfolio-description">${item.description}</p>
        </div>
    `;
    
    // Add click event for modal (you can implement modal functionality here)
    portfolioItem.addEventListener('click', () => {
        console.log('Portfolio item clicked:', item);
        // Implement modal or navigation to project details
    });
    
    return portfolioItem;
}

function filterPortfolio(data, filter) {
    const filteredData = filter === 'all' ? data : data.filter(item => item.category === filter);

    // Animate out current items
    gsap.to('.portfolio-item', {
        opacity: 0,
        y: 20,
        duration: 0.3,
        stagger: 0.05,
        onComplete: () => {
            // Render filtered items
            renderPortfolio(filteredData);
        }
    });
}

// ===== CONTACT FORM =====
function initContactForm() {
    const contactForm = document.querySelector('.contact-form');

    if (contactForm) {
        contactForm.addEventListener('submit', handleFormSubmit);

        // Add focus animations to form inputs
        const formInputs = contactForm.querySelectorAll('input, textarea');
        formInputs.forEach(input => {
            input.addEventListener('focus', () => {
                gsap.to(input, {
                    scale: 1.02,
                    duration: 0.2,
                    ease: 'power2.out'
                });
            });

            input.addEventListener('blur', () => {
                gsap.to(input, {
                    scale: 1,
                    duration: 0.2,
                    ease: 'power2.out'
                });
            });
        });
    }
}

function handleFormSubmit(e) {
    e.preventDefault();

    const formData = new FormData(e.target);
    const data = {
        name: formData.get('name'),
        email: formData.get('email'),
        subject: formData.get('subject'),
        message: formData.get('message')
    };

    // Show loading state
    const submitButton = e.target.querySelector('button[type="submit"]');
    const originalText = submitButton.textContent;
    submitButton.textContent = 'Sending...';
    submitButton.disabled = true;

    // Simulate form submission (replace with actual form handling)
    setTimeout(() => {
        // Reset form
        e.target.reset();

        // Reset button
        submitButton.textContent = originalText;
        submitButton.disabled = false;

        // Show success message
        showNotification('Message sent successfully!', 'success');

        console.log('Form submitted:', data);
    }, 2000);
}

// ===== SMOOTH SCROLLING =====
function initSmoothScrolling() {
    // Smooth scroll for navigation links
    const navLinks = document.querySelectorAll('a[href^="#"]');

    navLinks.forEach(link => {
        link.addEventListener('click', (e) => {
            e.preventDefault();

            const targetId = link.getAttribute('href');
            const targetSection = document.querySelector(targetId);

            if (targetSection) {
                const offsetTop = targetSection.offsetTop - 70; // Account for fixed navbar

                gsap.to(window, {
                    duration: 1,
                    scrollTo: offsetTop,
                    ease: 'power2.inOut'
                });
            }
        });
    });
}

// ===== UTILITY FUNCTIONS =====
function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <span class="notification-message">${message}</span>
            <button class="notification-close">&times;</button>
        </div>
    `;

    // Add styles
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#3b82f6'};
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 0.5rem;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        z-index: 10000;
        transform: translateX(100%);
        transition: transform 0.3s ease;
    `;

    // Add to DOM
    document.body.appendChild(notification);

    // Animate in
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);

    // Add close functionality
    const closeButton = notification.querySelector('.notification-close');
    closeButton.addEventListener('click', () => {
        removeNotification(notification);
    });

    // Auto remove after 5 seconds
    setTimeout(() => {
        removeNotification(notification);
    }, 5000);
}

function removeNotification(notification) {
    notification.style.transform = 'translateX(100%)';
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 300);
}

// ===== PERFORMANCE OPTIMIZATIONS =====
// Throttle scroll events
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    }
}

// Debounce resize events
function debounce(func, wait, immediate) {
    let timeout;
    return function() {
        const context = this, args = arguments;
        const later = function() {
            timeout = null;
            if (!immediate) func.apply(context, args);
        };
        const callNow = immediate && !timeout;
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
        if (callNow) func.apply(context, args);
    };
}

// ===== WINDOW RESIZE HANDLER =====
window.addEventListener('resize', debounce(() => {
    // Refresh ScrollTrigger on resize
    ScrollTrigger.refresh();
}, 250));

// ===== INTERSECTION OBSERVER FOR PERFORMANCE =====
const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
};

const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            entry.target.classList.add('in-view');
        }
    });
}, observerOptions);

// Observe elements for lazy loading or animations
document.addEventListener('DOMContentLoaded', () => {
    const elementsToObserve = document.querySelectorAll('.portfolio-item, .skill-item, .stat-item');
    elementsToObserve.forEach(el => observer.observe(el));
});

// ===== ACCESSIBILITY IMPROVEMENTS =====
// Keyboard navigation support
document.addEventListener('keydown', (e) => {
    if (e.key === 'Escape') {
        // Close mobile menu if open
        const hamburger = document.querySelector('.hamburger');
        const navMenu = document.querySelector('.nav-menu');

        if (navMenu.classList.contains('active')) {
            hamburger.classList.remove('active');
            navMenu.classList.remove('active');
        }
    }
});

// Focus management for better accessibility
document.addEventListener('DOMContentLoaded', () => {
    const focusableElements = document.querySelectorAll(
        'a[href], button, textarea, input[type="text"], input[type="radio"], input[type="checkbox"], select'
    );

    focusableElements.forEach(element => {
        element.addEventListener('focus', () => {
            element.style.outline = '2px solid var(--primary-color)';
            element.style.outlineOffset = '2px';
        });

        element.addEventListener('blur', () => {
            element.style.outline = 'none';
        });
    });
});

// ===== PRELOAD CRITICAL RESOURCES =====
function preloadCriticalResources() {
    // Preload hero image or other critical assets
    const criticalImages = [
        // Add your critical image URLs here
    ];

    criticalImages.forEach(src => {
        const img = new Image();
        img.src = src;
    });
}

// Initialize preloading
preloadCriticalResources();
