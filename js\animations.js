// ===== REVOLUTIONARY ANIMATION SYSTEM =====

class AdvancedAnimations {
    constructor() {
        this.isInitialized = false;
        this.animations = new Map();
        this.observers = new Map();
        this.timelines = new Map();
        
        this.init();
    }

    init() {
        if (typeof gsap === 'undefined') {
            console.warn('GSAP not loaded, animations disabled');
            return;
        }

        gsap.registerPlugin(ScrollTrigger, TextPlugin);
        this.setupDefaultAnimations();
        this.setupScrollAnimations();
        this.setupHoverAnimations();
        this.setupMicroInteractions();
        this.isInitialized = true;
    }

    setupDefaultAnimations() {
        // Hero entrance animation
        this.createHeroAnimation();
        
        // Section reveal animations
        this.createSectionAnimations();
        
        // Navigation animations
        this.createNavigationAnimations();
        
        // Button animations
        this.createButtonAnimations();
    }

    createHeroAnimation() {
        const tl = gsap.timeline({ paused: true });
        
        tl.set('.hero-badge', { opacity: 0, scale: 0.8, y: 30 })
          .set('.hero-title .word', { opacity: 0, y: 100, rotationX: 90 })
          .set('.hero-subtitle .subtitle-line', { opacity: 0, y: 50 })
          .set('.hero-actions .btn', { opacity: 0, y: 30, scale: 0.8 })
          .set('.hero-scroll', { opacity: 0, y: 20 });

        // Entrance sequence
        tl.to('.hero-badge', {
            opacity: 1,
            scale: 1,
            y: 0,
            duration: 1,
            ease: 'back.out(1.7)'
        })
        .to('.hero-title .word', {
            opacity: 1,
            y: 0,
            rotationX: 0,
            duration: 1.2,
            stagger: {
                amount: 0.6,
                from: 'start'
            },
            ease: 'power3.out'
        }, '-=0.5')
        .to('.hero-subtitle .subtitle-line', {
            opacity: 1,
            y: 0,
            duration: 0.8,
            stagger: 0.1,
            ease: 'power2.out'
        }, '-=0.8')
        .to('.hero-actions .btn', {
            opacity: 1,
            y: 0,
            scale: 1,
            duration: 0.8,
            stagger: 0.2,
            ease: 'back.out(1.7)'
        }, '-=0.6')
        .to('.hero-scroll', {
            opacity: 1,
            y: 0,
            duration: 0.6,
            ease: 'power2.out'
        }, '-=0.4');

        this.timelines.set('hero', tl);
    }

    createSectionAnimations() {
        // About section
        gsap.set('.about-content', { opacity: 0, y: 80 });
        gsap.set('.stat-item', { opacity: 0, y: 50, scale: 0.8 });

        ScrollTrigger.create({
            trigger: '.about',
            start: 'top 70%',
            onEnter: () => {
                gsap.to('.about-content', {
                    opacity: 1,
                    y: 0,
                    duration: 1,
                    ease: 'power3.out'
                });
                
                gsap.to('.stat-item', {
                    opacity: 1,
                    y: 0,
                    scale: 1,
                    duration: 0.8,
                    stagger: 0.2,
                    ease: 'back.out(1.7)',
                    delay: 0.3
                });
            }
        });

        // Services section
        gsap.set('.service-card', { opacity: 0, y: 100, rotationY: 45 });

        ScrollTrigger.create({
            trigger: '.services',
            start: 'top 70%',
            onEnter: () => {
                gsap.to('.service-card', {
                    opacity: 1,
                    y: 0,
                    rotationY: 0,
                    duration: 1,
                    stagger: {
                        amount: 0.8,
                        grid: [2, 2],
                        from: 'start'
                    },
                    ease: 'power3.out'
                });
            }
        });
    }

    createNavigationAnimations() {
        // Navigation scroll effect
        let lastScrollY = 0;
        const nav = document.getElementById('main-nav');

        ScrollTrigger.create({
            trigger: 'body',
            start: 'top top',
            end: 'bottom bottom',
            onUpdate: (self) => {
                const currentScrollY = self.scroll();
                
                if (currentScrollY > lastScrollY && currentScrollY > 100) {
                    // Scrolling down
                    gsap.to(nav, {
                        y: -100,
                        duration: 0.3,
                        ease: 'power2.out'
                    });
                } else {
                    // Scrolling up
                    gsap.to(nav, {
                        y: 0,
                        duration: 0.3,
                        ease: 'power2.out'
                    });
                }
                
                lastScrollY = currentScrollY;
            }
        });

        // Navigation link hover effects
        document.querySelectorAll('.nav-link').forEach(link => {
            const tl = gsap.timeline({ paused: true });
            
            tl.to(link, {
                scale: 1.1,
                duration: 0.3,
                ease: 'power2.out'
            })
            .to(link.querySelector('::before'), {
                scaleX: 1,
                duration: 0.3,
                ease: 'power2.out'
            }, 0);

            link.addEventListener('mouseenter', () => tl.play());
            link.addEventListener('mouseleave', () => tl.reverse());
        });
    }

    createButtonAnimations() {
        document.querySelectorAll('.btn').forEach(btn => {
            // Magnetic effect
            btn.addEventListener('mousemove', (e) => {
                const rect = btn.getBoundingClientRect();
                const x = e.clientX - rect.left - rect.width / 2;
                const y = e.clientY - rect.top - rect.height / 2;
                
                gsap.to(btn, {
                    x: x * 0.3,
                    y: y * 0.3,
                    duration: 0.3,
                    ease: 'power2.out'
                });
            });

            btn.addEventListener('mouseleave', () => {
                gsap.to(btn, {
                    x: 0,
                    y: 0,
                    duration: 0.5,
                    ease: 'elastic.out(1, 0.3)'
                });
            });

            // Click animation
            btn.addEventListener('click', (e) => {
                const ripple = btn.querySelector('.btn-ripple');
                if (ripple) {
                    const rect = btn.getBoundingClientRect();
                    const size = Math.max(rect.width, rect.height);
                    const x = e.clientX - rect.left - size / 2;
                    const y = e.clientY - rect.top - size / 2;
                    
                    gsap.set(ripple, {
                        width: 0,
                        height: 0,
                        x: e.clientX - rect.left,
                        y: e.clientY - rect.top,
                        opacity: 0.6
                    });
                    
                    gsap.to(ripple, {
                        width: size * 2,
                        height: size * 2,
                        x: x,
                        y: y,
                        opacity: 0,
                        duration: 0.6,
                        ease: 'power2.out'
                    });
                }
            });
        });
    }

    setupHoverAnimations() {
        // Service card hover effects
        document.querySelectorAll('.service-card').forEach(card => {
            const icon = card.querySelector('.service-icon');
            const title = card.querySelector('.service-title');
            
            const tl = gsap.timeline({ paused: true });
            
            tl.to(card, {
                y: -10,
                scale: 1.02,
                duration: 0.3,
                ease: 'power2.out'
            })
            .to(icon, {
                scale: 1.2,
                rotation: 5,
                duration: 0.3,
                ease: 'back.out(1.7)'
            }, 0)
            .to(title, {
                color: '#ffc321',
                duration: 0.3,
                ease: 'power2.out'
            }, 0);

            card.addEventListener('mouseenter', () => tl.play());
            card.addEventListener('mouseleave', () => tl.reverse());
        });

        // Stat item hover effects
        document.querySelectorAll('.stat-item').forEach(item => {
            const number = item.querySelector('.stat-number');
            
            item.addEventListener('mouseenter', () => {
                gsap.to(item, {
                    scale: 1.05,
                    duration: 0.3,
                    ease: 'back.out(1.7)'
                });
                
                gsap.to(number, {
                    scale: 1.2,
                    color: '#ffc321',
                    duration: 0.3,
                    ease: 'power2.out'
                });
            });

            item.addEventListener('mouseleave', () => {
                gsap.to(item, {
                    scale: 1,
                    duration: 0.3,
                    ease: 'power2.out'
                });
                
                gsap.to(number, {
                    scale: 1,
                    color: '#ffc321',
                    duration: 0.3,
                    ease: 'power2.out'
                });
            });
        });
    }

    setupMicroInteractions() {
        // Scroll indicator animation
        const scrollIndicator = document.querySelector('.scroll-indicator');
        if (scrollIndicator) {
            gsap.to(scrollIndicator.querySelector('.scroll-line'), {
                y: 30,
                opacity: 0,
                duration: 2,
                repeat: -1,
                ease: 'power2.inOut'
            });
        }

        // Logo pulse animation
        const logo = document.querySelector('.nav-logo .logo');
        if (logo) {
            gsap.to(logo, {
                scale: 1.05,
                duration: 2,
                repeat: -1,
                yoyo: true,
                ease: 'power2.inOut'
            });
        }

        // Badge glow animation
        const badge = document.querySelector('.hero-badge');
        if (badge) {
            gsap.to(badge.querySelector('.badge-glow'), {
                scale: 1.2,
                opacity: 0.3,
                duration: 3,
                repeat: -1,
                yoyo: true,
                ease: 'power2.inOut'
            });
        }
    }

    setupScrollAnimations() {
        // Parallax effects
        gsap.utils.toArray('.hero-shapes .shape').forEach((shape, index) => {
            gsap.to(shape, {
                y: -100 * (index + 1),
                scrollTrigger: {
                    trigger: '.hero',
                    start: 'top bottom',
                    end: 'bottom top',
                    scrub: true
                }
            });
        });

        // Text reveal animations
        gsap.utils.toArray('.section-title').forEach(title => {
            const chars = title.textContent.split('');
            title.innerHTML = chars.map(char => `<span class="char">${char}</span>`).join('');
            
            gsap.fromTo(title.querySelectorAll('.char'), 
                { opacity: 0, y: 50, rotationX: 90 },
                {
                    opacity: 1,
                    y: 0,
                    rotationX: 0,
                    duration: 0.8,
                    stagger: 0.02,
                    ease: 'back.out(1.7)',
                    scrollTrigger: {
                        trigger: title,
                        start: 'top 80%',
                        toggleActions: 'play none none reverse'
                    }
                }
            );
        });
    }

    // Public methods
    playHeroAnimation() {
        const heroTl = this.timelines.get('hero');
        if (heroTl) {
            heroTl.play();
        }
    }

    createCustomAnimation(selector, properties, options = {}) {
        return gsap.to(selector, { ...properties, ...options });
    }

    createScrollAnimation(trigger, animation) {
        return ScrollTrigger.create({
            trigger: trigger,
            ...animation
        });
    }

    pauseAllAnimations() {
        gsap.globalTimeline.pause();
    }

    resumeAllAnimations() {
        gsap.globalTimeline.resume();
    }

    killAllAnimations() {
        gsap.killTweensOf('*');
        ScrollTrigger.killAll();
    }

    // Performance optimization
    enablePerformanceMode() {
        gsap.config({
            force3D: true,
            nullTargetWarn: false
        });
    }

    disableAnimationsOnLowPerformance() {
        if (navigator.hardwareConcurrency < 4) {
            this.killAllAnimations();
            console.log('Animations disabled due to low performance device');
        }
    }
}

// Initialize Advanced Animations
window.addEventListener('DOMContentLoaded', () => {
    if (typeof gsap !== 'undefined') {
        window.advancedAnimations = new AdvancedAnimations();
        console.log('Advanced Animations initialized successfully');
    } else {
        console.warn('GSAP not loaded, advanced animations disabled');
    }
});

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AdvancedAnimations;
}
