/* ===== CSS CUSTOM PROPERTIES ===== */
:root {
  /* Colors */
  --primary-color: #2563eb;
  --primary-dark: #1d4ed8;
  --secondary-color: #64748b;
  --accent-color: #f59e0b;
  --text-primary: #1e293b;
  --text-secondary: #64748b;
  --text-light: #94a3b8;
  --background: #ffffff;
  --background-secondary: #f8fafc;
  --border-color: #e2e8f0;
  --shadow-light: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-medium: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-large: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  
  /* Typography */
  --font-primary: 'Inter', sans-serif;
  --font-display: 'Playfair Display', serif;
  
  /* Spacing */
  --container-max-width: 1200px;
  --section-padding: 5rem 0;
  --element-spacing: 2rem;
  
  /* Transitions */
  --transition-fast: 0.2s ease;
  --transition-medium: 0.3s ease;
  --transition-slow: 0.5s ease;
  
  /* Border Radius */
  --radius-small: 0.375rem;
  --radius-medium: 0.5rem;
  --radius-large: 1rem;
}

/* ===== RESET & BASE STYLES ===== */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  font-size: 16px;
}

body {
  font-family: var(--font-primary);
  line-height: 1.6;
  color: var(--text-primary);
  background-color: var(--background);
  overflow-x: hidden;
}

/* ===== TYPOGRAPHY ===== */
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-display);
  font-weight: 600;
  line-height: 1.2;
  margin-bottom: 1rem;
}

h1 { font-size: clamp(2.5rem, 5vw, 4rem); }
h2 { font-size: clamp(2rem, 4vw, 3rem); }
h3 { font-size: clamp(1.5rem, 3vw, 2rem); }
h4 { font-size: clamp(1.25rem, 2.5vw, 1.5rem); }

p {
  margin-bottom: 1rem;
  color: var(--text-secondary);
}

a {
  text-decoration: none;
  color: inherit;
  transition: var(--transition-fast);
}

/* ===== UTILITY CLASSES ===== */
.container {
  max-width: var(--container-max-width);
  margin: 0 auto;
  padding: 0 1rem;
}

.btn {
  display: inline-block;
  padding: 0.875rem 2rem;
  border-radius: var(--radius-medium);
  font-weight: 500;
  text-align: center;
  transition: var(--transition-medium);
  cursor: pointer;
  border: none;
  font-size: 1rem;
  text-decoration: none;
}

.btn-primary {
  background-color: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background-color: var(--primary-dark);
  transform: translateY(-2px);
  box-shadow: var(--shadow-medium);
}

.btn-secondary {
  background-color: transparent;
  color: var(--primary-color);
  border: 2px solid var(--primary-color);
}

.btn-secondary:hover {
  background-color: var(--primary-color);
  color: white;
  transform: translateY(-2px);
}

/* ===== LOADING SCREEN ===== */
.loader {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: var(--background);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loader-content {
  text-align: center;
}

.loader-text {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 2rem;
  color: var(--text-primary);
}

.loader-progress {
  width: 200px;
  height: 4px;
  background-color: var(--border-color);
  border-radius: 2px;
  overflow: hidden;
}

.loader-bar {
  width: 0%;
  height: 100%;
  background-color: var(--primary-color);
  border-radius: 2px;
  transition: width 0.3s ease;
}

/* ===== NAVIGATION ===== */
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  background-color: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--border-color);
  z-index: 1000;
  transition: var(--transition-medium);
}

.nav-container {
  max-width: var(--container-max-width);
  margin: 0 auto;
  padding: 0 1rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 70px;
}

.nav-logo a {
  font-family: var(--font-display);
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary);
}

.nav-menu {
  display: flex;
  list-style: none;
  gap: 2rem;
}

.nav-link {
  color: var(--text-secondary);
  font-weight: 500;
  position: relative;
}

.nav-link:hover,
.nav-link.active {
  color: var(--primary-color);
}

.nav-link::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 0;
  height: 2px;
  background-color: var(--primary-color);
  transition: var(--transition-medium);
}

.nav-link:hover::after,
.nav-link.active::after {
  width: 100%;
}

/* Hamburger Menu */
.hamburger {
  display: none;
  flex-direction: column;
  cursor: pointer;
}

.bar {
  width: 25px;
  height: 3px;
  background-color: var(--text-primary);
  margin: 3px 0;
  transition: var(--transition-fast);
}

/* ===== HERO SECTION ===== */
.hero {
  min-height: 100vh;
  display: flex;
  align-items: center;
  position: relative;
  padding-top: 70px;
}

.hero-container {
  max-width: var(--container-max-width);
  margin: 0 auto;
  padding: 0 1rem;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
}

.hero-content {
  z-index: 2;
}

.hero-title {
  margin-bottom: 2rem;
}

.hero-greeting {
  display: block;
  font-size: 1.25rem;
  color: var(--text-secondary);
  font-weight: 400;
  margin-bottom: 0.5rem;
}

.hero-name {
  display: block;
  font-size: clamp(3rem, 6vw, 5rem);
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.hero-role {
  display: block;
  font-size: clamp(1.5rem, 3vw, 2.5rem);
  color: var(--primary-color);
  font-weight: 500;
}

.hero-description {
  font-size: 1.125rem;
  margin-bottom: 2.5rem;
  max-width: 500px;
}

.hero-buttons {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.hero-visual {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.hero-image {
  width: 400px;
  height: 400px;
  position: relative;
}

.hero-shape {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  border-radius: 50% 20% 50% 20%;
  position: relative;
  animation: float 6s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(5deg); }
}

.scroll-indicator {
  position: absolute;
  bottom: 2rem;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.scroll-line {
  width: 2px;
  height: 30px;
  background-color: var(--text-light);
  position: relative;
  overflow: hidden;
}

.scroll-line::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: var(--primary-color);
  animation: scroll-animation 2s infinite;
}

@keyframes scroll-animation {
  0% { transform: translateY(-100%); }
  100% { transform: translateY(100%); }
}

.scroll-text {
  font-size: 0.875rem;
  color: var(--text-light);
  writing-mode: vertical-rl;
  text-orientation: mixed;
}

/* ===== SECTION STYLES ===== */
section {
  padding: var(--section-padding);
}

.section-header {
  text-align: center;
  margin-bottom: 4rem;
}

.section-title {
  color: var(--text-primary);
  margin-bottom: 1rem;
}

.section-subtitle {
  font-size: 1.125rem;
  color: var(--text-secondary);
  max-width: 600px;
  margin: 0 auto;
}

/* ===== ABOUT SECTION ===== */
.about {
  background-color: var(--background-secondary);
}

.about-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 4rem;
  align-items: start;
}

.about-text h3 {
  color: var(--text-primary);
  margin-bottom: 1.5rem;
  font-size: 1.5rem;
}

.about-text p {
  font-size: 1.125rem;
  margin-bottom: 2rem;
}

.skills h4 {
  color: var(--text-primary);
  margin-bottom: 1.5rem;
  font-size: 1.25rem;
}

.skills-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 1rem;
}

.skill-item {
  background-color: var(--background);
  padding: 0.75rem 1rem;
  border-radius: var(--radius-medium);
  text-align: center;
  font-weight: 500;
  color: var(--text-primary);
  border: 2px solid var(--border-color);
  transition: var(--transition-medium);
}

.skill-item:hover {
  border-color: var(--primary-color);
  transform: translateY(-2px);
  box-shadow: var(--shadow-light);
}

.about-stats {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.stat-item {
  text-align: center;
  padding: 2rem;
  background-color: var(--background);
  border-radius: var(--radius-large);
  box-shadow: var(--shadow-light);
  transition: var(--transition-medium);
}

.stat-item:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-medium);
}

.stat-number {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--primary-color);
  font-family: var(--font-display);
}

.stat-label {
  color: var(--text-secondary);
  font-weight: 500;
  margin-top: 0.5rem;
}

/* ===== PORTFOLIO SECTION ===== */
.portfolio-filters {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 3rem;
  flex-wrap: wrap;
}

.filter-btn {
  padding: 0.75rem 1.5rem;
  background-color: transparent;
  border: 2px solid var(--border-color);
  border-radius: var(--radius-medium);
  color: var(--text-secondary);
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition-medium);
}

.filter-btn:hover,
.filter-btn.active {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  color: white;
}

.portfolio-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
}

.portfolio-item {
  background-color: var(--background);
  border-radius: var(--radius-large);
  overflow: hidden;
  box-shadow: var(--shadow-light);
  transition: var(--transition-medium);
  cursor: pointer;
}

.portfolio-item:hover {
  transform: translateY(-10px);
  box-shadow: var(--shadow-large);
}

.portfolio-image {
  width: 100%;
  height: 250px;
  background-color: var(--background-secondary);
  position: relative;
  overflow: hidden;
}

.portfolio-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: var(--transition-slow);
}

.portfolio-item:hover .portfolio-image img {
  transform: scale(1.1);
}

.portfolio-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(37, 99, 235, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: var(--transition-medium);
}

.portfolio-item:hover .portfolio-overlay {
  opacity: 1;
}

.portfolio-overlay-content {
  text-align: center;
  color: white;
}

.portfolio-overlay-content h4 {
  margin-bottom: 0.5rem;
}

.portfolio-content {
  padding: 1.5rem;
}

.portfolio-title {
  font-size: 1.25rem;
  margin-bottom: 0.5rem;
  color: var(--text-primary);
}

.portfolio-category {
  color: var(--primary-color);
  font-weight: 500;
  font-size: 0.875rem;
  margin-bottom: 1rem;
}

.portfolio-description {
  color: var(--text-secondary);
  font-size: 0.95rem;
  line-height: 1.6;
}

/* ===== CONTACT SECTION ===== */
.contact {
  background-color: var(--background-secondary);
}

.contact-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: start;
}

.contact-info h3 {
  color: var(--text-primary);
  margin-bottom: 1rem;
}

.contact-info p {
  font-size: 1.125rem;
  margin-bottom: 2rem;
}

.contact-details {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.contact-icon {
  width: 50px;
  height: 50px;
  background-color: var(--primary-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
}

.contact-text {
  display: flex;
  flex-direction: column;
}

.contact-text span:first-child {
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.25rem;
}

.contact-text a {
  color: var(--primary-color);
  transition: var(--transition-fast);
}

.contact-text a:hover {
  text-decoration: underline;
}

.contact-form {
  background-color: var(--background);
  padding: 2rem;
  border-radius: var(--radius-large);
  box-shadow: var(--shadow-light);
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 1rem;
  border: 2px solid var(--border-color);
  border-radius: var(--radius-medium);
  font-family: var(--font-primary);
  font-size: 1rem;
  transition: var(--transition-fast);
  background-color: var(--background);
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.form-group textarea {
  resize: vertical;
  min-height: 120px;
}

/* ===== FOOTER ===== */
.footer {
  background-color: var(--text-primary);
  color: white;
  padding: 2rem 0;
}

.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
}

.footer-text p {
  color: var(--text-light);
  margin: 0;
}

.social-links {
  display: flex;
  gap: 1.5rem;
}

.social-link {
  color: var(--text-light);
  font-weight: 500;
  transition: var(--transition-fast);
}

.social-link:hover {
  color: white;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
  /* Navigation */
  .nav-menu {
    position: fixed;
    left: -100%;
    top: 70px;
    flex-direction: column;
    background-color: var(--background);
    width: 100%;
    text-align: center;
    transition: var(--transition-medium);
    box-shadow: var(--shadow-medium);
    padding: 2rem 0;
  }

  .nav-menu.active {
    left: 0;
  }

  .nav-menu li {
    margin: 1rem 0;
  }

  .hamburger {
    display: flex;
  }

  .hamburger.active .bar:nth-child(2) {
    opacity: 0;
  }

  .hamburger.active .bar:nth-child(1) {
    transform: translateY(8px) rotate(45deg);
  }

  .hamburger.active .bar:nth-child(3) {
    transform: translateY(-8px) rotate(-45deg);
  }

  /* Hero Section */
  .hero-container {
    grid-template-columns: 1fr;
    gap: 2rem;
    text-align: center;
  }

  .hero-visual {
    order: -1;
  }

  .hero-image {
    width: 300px;
    height: 300px;
  }

  .hero-buttons {
    justify-content: center;
  }

  /* About Section */
  .about-content {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .about-stats {
    flex-direction: row;
    justify-content: space-around;
  }

  .stat-item {
    flex: 1;
    margin: 0 0.5rem;
  }

  /* Portfolio */
  .portfolio-grid {
    grid-template-columns: 1fr;
  }

  .portfolio-filters {
    justify-content: center;
  }

  .filter-btn {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
  }

  /* Contact */
  .contact-content {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  /* Footer */
  .footer-content {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }
}

@media (max-width: 480px) {
  /* Typography adjustments */
  .hero-name {
    font-size: 2.5rem;
  }

  .hero-role {
    font-size: 1.5rem;
  }

  .section-title {
    font-size: 2rem;
  }

  /* Spacing adjustments */
  :root {
    --section-padding: 3rem 0;
  }

  .container {
    padding: 0 0.75rem;
  }

  /* Hero adjustments */
  .hero-image {
    width: 250px;
    height: 250px;
  }

  .hero-buttons {
    flex-direction: column;
    align-items: center;
  }

  .btn {
    width: 100%;
    max-width: 250px;
  }

  /* About stats */
  .about-stats {
    flex-direction: column;
  }

  .stat-item {
    margin: 0;
  }

  /* Portfolio grid */
  .portfolio-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .portfolio-item {
    margin: 0 auto;
    max-width: 350px;
  }

  /* Contact form */
  .contact-form {
    padding: 1.5rem;
  }

  /* Skills grid */
  .skills-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* ===== ANIMATIONS & TRANSITIONS ===== */
.fade-in {
  opacity: 0;
  transform: translateY(30px);
}

.fade-in-left {
  opacity: 0;
  transform: translateX(-30px);
}

.fade-in-right {
  opacity: 0;
  transform: translateX(30px);
}

.scale-in {
  opacity: 0;
  transform: scale(0.8);
}

/* ===== UTILITY CLASSES ===== */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-1 { margin-bottom: 0.5rem; }
.mb-2 { margin-bottom: 1rem; }
.mb-3 { margin-bottom: 1.5rem; }
.mb-4 { margin-bottom: 2rem; }

.mt-1 { margin-top: 0.5rem; }
.mt-2 { margin-top: 1rem; }
.mt-3 { margin-top: 1.5rem; }
.mt-4 { margin-top: 2rem; }

.hidden { display: none; }
.visible { display: block; }

/* ===== SMOOTH SCROLLING ===== */
@media (prefers-reduced-motion: no-preference) {
  html {
    scroll-behavior: smooth;
  }
}
