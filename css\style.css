/* ===== RESET & BASE ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Colors */
    --primary: #ffc321;
    --primary-dark: #e6b01e;
    --secondary: #1a1a1a;
    --accent: #6366f1;
    --text-primary: #ffffff;
    --text-secondary: #a1a1aa;
    --bg-primary: #0a0a0a;
    --bg-secondary: #111111;
    
    /* Typography */
    --font-primary: 'Inter', sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    --font-size-5xl: 3rem;
    --font-size-6xl: 3.75rem;
    --font-size-7xl: 4.5rem;
    --font-size-8xl: 6rem;
    
    /* Spacing */
    --space-1: 0.25rem;
    --space-2: 0.5rem;
    --space-3: 0.75rem;
    --space-4: 1rem;
    --space-5: 1.25rem;
    --space-6: 1.5rem;
    --space-8: 2rem;
    --space-10: 2.5rem;
    --space-12: 3rem;
    --space-16: 4rem;
    --space-20: 5rem;
    --space-24: 6rem;
    --space-32: 8rem;
    
    /* Borders */
    --radius-sm: 0.125rem;
    --radius: 0.25rem;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --radius-xl: 0.75rem;
    --radius-2xl: 1rem;
    --radius-3xl: 1.5rem;
    --radius-full: 9999px;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);
    
    /* Transitions */
    --transition-fast: 150ms ease;
    --transition-base: 300ms ease;
    --transition-slow: 500ms ease;
    
    /* Z-index */
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal-backdrop: 1040;
    --z-modal: 1050;
    --z-popover: 1060;
    --z-tooltip: 1070;
    --z-toast: 1080;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-primary);
    background: var(--bg-primary);
    color: var(--text-primary);
    line-height: 1.6;
    overflow-x: hidden;
    cursor: none;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--space-6);
}

/* ===== CURSOR ===== */
.cursor {
    position: fixed;
    width: 20px;
    height: 20px;
    background: var(--primary);
    border-radius: 50%;
    pointer-events: none;
    z-index: 9999;
    mix-blend-mode: difference;
    transition: transform 0.1s ease;
}

.cursor-follower {
    position: fixed;
    width: 40px;
    height: 40px;
    border: 2px solid var(--primary);
    border-radius: 50%;
    pointer-events: none;
    z-index: 9998;
    transition: all 0.3s ease;
}

/* ===== NAVIGATION ===== */
.nav {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: var(--z-fixed);
    padding: var(--space-6) 0;
    backdrop-filter: blur(20px);
    background: rgba(10, 10, 10, 0.8);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--space-6);
}

.nav-logo .logo {
    height: 40px;
    width: auto;
}

.nav-links {
    display: flex;
    gap: var(--space-8);
    align-items: center;
}

.nav-link {
    color: var(--text-primary);
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition-base);
    position: relative;
}

.nav-link:hover {
    color: var(--primary);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -4px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--primary);
    transition: width var(--transition-base);
}

.nav-link:hover::after {
    width: 100%;
}

.nav-cta {
    background: var(--primary);
    color: var(--bg-primary) !important;
    padding: var(--space-3) var(--space-6);
    border-radius: var(--radius-full);
    font-weight: 600;
}

.nav-cta:hover {
    background: var(--primary-dark);
    transform: translateY(-2px);
}

.nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.nav-toggle span {
    width: 25px;
    height: 3px;
    background: var(--text-primary);
    margin: 3px 0;
    transition: var(--transition-base);
}

/* ===== HERO SECTION ===== */
.hero {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    position: relative;
    overflow: hidden;
    text-align: center;
}

.hero-bg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: -1;
}

.hero-shapes {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

.shape {
    position: absolute;
    border-radius: 50%;
    filter: blur(100px);
    opacity: 0.3;
    animation: float 20s ease-in-out infinite;
}

.shape-1 {
    width: 300px;
    height: 300px;
    background: linear-gradient(45deg, var(--primary), var(--accent));
    top: 10%;
    left: 10%;
    animation-delay: 0s;
}

.shape-2 {
    width: 200px;
    height: 200px;
    background: linear-gradient(45deg, var(--accent), #ec4899);
    top: 60%;
    right: 10%;
    animation-delay: 5s;
}

.shape-3 {
    width: 250px;
    height: 250px;
    background: linear-gradient(45deg, #10b981, var(--primary));
    bottom: 20%;
    left: 20%;
    animation-delay: 10s;
}

.shape-4 {
    width: 180px;
    height: 180px;
    background: linear-gradient(45deg, #f59e0b, #ef4444);
    top: 30%;
    right: 30%;
    animation-delay: 15s;
}

.hero-content {
    z-index: 1;
    max-width: 800px;
    margin: 0 auto;
    padding: 0 var(--space-6);
}

.hero-badge {
    display: inline-block;
    background: rgba(255, 195, 33, 0.1);
    border: 1px solid rgba(255, 195, 33, 0.3);
    color: var(--primary);
    padding: var(--space-2) var(--space-4);
    border-radius: var(--radius-full);
    font-size: var(--font-size-sm);
    font-weight: 600;
    margin-bottom: var(--space-8);
    backdrop-filter: blur(10px);
}

.hero-title {
    font-size: clamp(3rem, 8vw, 8rem);
    font-weight: 900;
    line-height: 0.9;
    margin-bottom: var(--space-8);
    letter-spacing: -0.02em;
}

.title-line {
    display: block;
    overflow: hidden;
}

.word {
    display: inline-block;
    background: linear-gradient(135deg, var(--text-primary) 0%, var(--primary) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.morphing-word {
    background: linear-gradient(135deg, var(--primary) 0%, var(--accent) 50%, #ec4899 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    background-size: 200% 200%;
    animation: gradientShift 3s ease-in-out infinite;
}

.char {
    display: inline-block;
    transition: all 0.3s ease;
}

.hero-subtitle {
    font-size: var(--font-size-xl);
    color: var(--text-secondary);
    margin-bottom: var(--space-12);
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.subtitle-line {
    margin-bottom: var(--space-2);
}

.hero-actions {
    display: flex;
    gap: var(--space-6);
    justify-content: center;
    margin-bottom: var(--space-16);
    flex-wrap: wrap;
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: var(--space-2);
    padding: var(--space-4) var(--space-8);
    border-radius: var(--radius-full);
    font-weight: 600;
    text-decoration: none;
    transition: all var(--transition-base);
    cursor: pointer;
    border: none;
    font-size: var(--font-size-base);
    position: relative;
    overflow: hidden;
}

.btn-primary {
    background: var(--primary);
    color: var(--bg-primary);
}

.btn-primary:hover {
    background: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(255, 195, 33, 0.3);
}

.btn-secondary {
    background: transparent;
    color: var(--text-primary);
    border: 2px solid rgba(255, 255, 255, 0.2);
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: var(--primary);
    transform: translateY(-2px);
}

.btn-arrow {
    transition: transform var(--transition-base);
}

.btn:hover .btn-arrow {
    transform: translateX(4px);
}

.hero-scroll {
    position: absolute;
    bottom: var(--space-8);
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--space-4);
}

.scroll-text {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    font-weight: 500;
}

.scroll-line {
    width: 2px;
    height: 40px;
    background: linear-gradient(to bottom, var(--primary), transparent);
    animation: scrollPulse 2s ease-in-out infinite;
}

/* ===== ANIMATIONS ===== */
@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
    }
    33% {
        transform: translateY(-20px) rotate(120deg);
    }
    66% {
        transform: translateY(10px) rotate(240deg);
    }
}

@keyframes gradientShift {
    0%, 100% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
}

@keyframes scrollPulse {
    0%, 100% {
        opacity: 0.3;
        transform: scaleY(1);
    }
    50% {
        opacity: 1;
        transform: scaleY(1.2);
    }
}

/* ===== RESPONSIVE ===== */
@media (max-width: 768px) {
    .nav-links {
        display: none;
    }
    
    .nav-toggle {
        display: flex;
    }
    
    .hero-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .btn {
        width: 100%;
        max-width: 300px;
        justify-content: center;
    }
    
    body {
        cursor: auto;
    }
    
    .cursor,
    .cursor-follower {
        display: none;
    }
}

/* ===== SECTIONS ===== */
.section {
    padding: var(--space-24) 0;
}

.section-title {
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 800;
    line-height: 1.1;
    margin-bottom: var(--space-6);
    text-align: center;
}

.title-word {
    display: inline-block;
    margin-right: var(--space-2);
}

.highlight {
    background: linear-gradient(135deg, var(--primary) 0%, var(--accent) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.section-subtitle {
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
    text-align: center;
    max-width: 600px;
    margin: 0 auto var(--space-16);
}

/* ===== WHY SECTION ===== */
.why-section {
    padding: var(--space-24) 0;
    background: linear-gradient(135deg, rgba(255, 195, 33, 0.05) 0%, rgba(99, 102, 241, 0.05) 100%);
}

.why-header {
    text-align: center;
    margin-bottom: var(--space-16);
}

.why-content {
    max-width: 1000px;
    margin: 0 auto;
}

.why-text {
    text-align: center;
    margin-bottom: var(--space-16);
}

.why-description {
    font-size: var(--font-size-xl);
    line-height: 1.6;
    color: var(--text-secondary);
}

.highlight-text {
    color: var(--primary);
    font-weight: 600;
}

.why-features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--space-8);
    margin-top: var(--space-16);
}

.feature-item {
    display: flex;
    gap: var(--space-4);
    padding: var(--space-6);
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--radius-xl);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all var(--transition-base);
}

.feature-item:hover {
    transform: translateY(-4px);
    background: rgba(255, 255, 255, 0.08);
    border-color: rgba(255, 195, 33, 0.3);
}

.feature-icon {
    font-size: var(--font-size-2xl);
    flex-shrink: 0;
}

.feature-content h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin-bottom: var(--space-2);
    color: var(--text-primary);
}

.feature-content p {
    color: var(--text-secondary);
    line-height: 1.5;
}

/* ===== SERVICES SECTION ===== */
.services-section {
    padding: var(--space-24) 0;
}

.services-header {
    text-align: center;
    margin-bottom: var(--space-16);
}

.services-grid {
    display: grid;
    gap: var(--space-6);
    max-width: 800px;
    margin: 0 auto;
}

.service-card {
    display: flex;
    align-items: center;
    gap: var(--space-6);
    padding: var(--space-8);
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-xl);
    transition: all var(--transition-base);
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.service-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 195, 33, 0.1), transparent);
    transition: left 0.5s ease;
}

.service-card:hover::before {
    left: 100%;
}

.service-card:hover {
    transform: translateX(8px);
    background: rgba(255, 255, 255, 0.05);
    border-color: rgba(255, 195, 33, 0.3);
}

.service-number {
    font-size: var(--font-size-xl);
    font-weight: 800;
    color: var(--primary);
    flex-shrink: 0;
    width: 60px;
}

.service-content {
    flex: 1;
}

.service-title {
    font-size: var(--font-size-xl);
    font-weight: 700;
    margin-bottom: var(--space-2);
    color: var(--text-primary);
}

.service-desc {
    color: var(--text-secondary);
    margin-bottom: var(--space-4);
    line-height: 1.5;
}

.service-tags {
    display: flex;
    gap: var(--space-2);
    flex-wrap: wrap;
}

.tag {
    background: rgba(255, 195, 33, 0.1);
    color: var(--primary);
    padding: var(--space-1) var(--space-3);
    border-radius: var(--radius-full);
    font-size: var(--font-size-xs);
    font-weight: 500;
    border: 1px solid rgba(255, 195, 33, 0.2);
}

.service-arrow {
    font-size: var(--font-size-xl);
    color: var(--primary);
    transition: transform var(--transition-base);
    flex-shrink: 0;
}

.service-card:hover .service-arrow {
    transform: translateX(4px);
}

/* ===== WORK SECTION ===== */
.work-section {
    padding: var(--space-24) 0;
    background: rgba(255, 255, 255, 0.02);
}

.work-header {
    text-align: center;
    margin-bottom: var(--space-16);
}

.work-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--space-8);
    margin-bottom: var(--space-16);
}

.work-item {
    background: rgba(255, 255, 255, 0.05);
    border-radius: var(--radius-xl);
    overflow: hidden;
    transition: all var(--transition-base);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.work-item:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-2xl);
    border-color: rgba(255, 195, 33, 0.3);
}

.work-image {
    position: relative;
    height: 250px;
    overflow: hidden;
}

.work-preview {
    width: 100%;
    height: 100%;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.preview-bg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    opacity: 0.8;
}

.preview-content {
    position: relative;
    z-index: 1;
    text-align: center;
    color: white;
}

.preview-title {
    font-size: var(--font-size-xl);
    font-weight: 700;
    margin-bottom: var(--space-2);
}

.preview-subtitle {
    font-size: var(--font-size-sm);
    opacity: 0.9;
}

.work-info {
    padding: var(--space-6);
}

.work-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin-bottom: var(--space-2);
    color: var(--text-primary);
}

.work-desc {
    color: var(--text-secondary);
    margin-bottom: var(--space-4);
    line-height: 1.5;
}

.work-tags {
    display: flex;
    gap: var(--space-2);
    flex-wrap: wrap;
}

.work-cta {
    text-align: center;
}

.btn-outline {
    background: transparent;
    color: var(--text-primary);
    border: 2px solid rgba(255, 255, 255, 0.2);
}

.btn-outline:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: var(--primary);
    transform: translateY(-2px);
}

/* ===== STATS SECTION ===== */
.stats-section {
    padding: var(--space-20) 0;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--space-8);
    max-width: 800px;
    margin: 0 auto;
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: var(--font-size-5xl);
    font-weight: 900;
    background: linear-gradient(135deg, var(--primary) 0%, var(--accent) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    line-height: 1;
    margin-bottom: var(--space-2);
}

.stat-label {
    color: var(--text-secondary);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    font-size: var(--font-size-sm);
}

/* ===== CTA SECTION ===== */
.cta-section {
    padding: var(--space-24) 0;
    background: linear-gradient(135deg, rgba(255, 195, 33, 0.1) 0%, rgba(99, 102, 241, 0.1) 100%);
}

.cta-content {
    text-align: center;
    max-width: 600px;
    margin: 0 auto;
}

.cta-title {
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 800;
    line-height: 1.1;
    margin-bottom: var(--space-6);
}

.cta-subtitle {
    font-size: var(--font-size-xl);
    color: var(--text-secondary);
    margin-bottom: var(--space-8);
}

.cta-actions {
    display: flex;
    justify-content: center;
}

.btn-large {
    padding: var(--space-5) var(--space-10);
    font-size: var(--font-size-lg);
}

/* ===== RESPONSIVE UPDATES ===== */
@media (max-width: 768px) {
    .services-grid {
        gap: var(--space-4);
    }

    .service-card {
        flex-direction: column;
        text-align: center;
        gap: var(--space-4);
    }

    .service-card:hover {
        transform: translateY(-4px);
    }

    .work-grid {
        grid-template-columns: 1fr;
        gap: var(--space-6);
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--space-6);
    }

    .why-features {
        grid-template-columns: 1fr;
        gap: var(--space-6);
    }

    .feature-item {
        flex-direction: column;
        text-align: center;
        gap: var(--space-3);
    }
}
