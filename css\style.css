/* ===== CUSTOM FONT FACES ===== */
@font-face {
  font-family: 'NewBlackTypeface';
  src: url('../fonts/NewBlackTypeface-UltraLight.woff2') format('woff2');
  font-weight: 100;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'NewBlackTypeface';
  src: url('../fonts/NewBlackTypeface-Light.woff2') format('woff2');
  font-weight: 300;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'NewBlackTypeface';
  src: url('../fonts/NewBlackTypeface-Regular.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'NewBlackTypeface';
  src: url('../fonts/NewBlackTypeface-Medium.woff2') format('woff2');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'NewBlackTypeface';
  src: url('../fonts/NewBlackTypeface-SemiBold.woff2') format('woff2');
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'NewBlackTypeface';
  src: url('../fonts/NewBlackTypeface-Bold.woff2') format('woff2');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'NewBlackTypeface';
  src: url('../fonts/NewBlackTypeface-ExtraBold.woff2') format('woff2');
  font-weight: 800;
  font-style: normal;
  font-display: swap;
}

/* ===== ADVANCED CSS CUSTOM PROPERTIES ===== */
:root {
  /* Advanced Color Palette */
  --primary-50: #eff6ff;
  --primary-100: #dbeafe;
  --primary-200: #bfdbfe;
  --primary-300: #93c5fd;
  --primary-400: #60a5fa;
  --primary-500: #3b82f6;
  --primary-600: #2563eb;
  --primary-700: #1d4ed8;
  --primary-800: #1e40af;
  --primary-900: #1e3a8a;

  --accent-50: #fefce8;
  --accent-100: #fef9c3;
  --accent-200: #fef08a;
  --accent-300: #fde047;
  --accent-400: #facc15;
  --accent-500: #eab308;
  --accent-600: #ca8a04;
  --accent-700: #a16207;
  --accent-800: #854d0e;
  --accent-900: #713f12;

  --neutral-50: #fafafa;
  --neutral-100: #f5f5f5;
  --neutral-200: #e5e5e5;
  --neutral-300: #d4d4d4;
  --neutral-400: #a3a3a3;
  --neutral-500: #737373;
  --neutral-600: #525252;
  --neutral-700: #404040;
  --neutral-800: #262626;
  --neutral-900: #171717;

  /* Semantic Colors */
  --primary-color: var(--primary-600);
  --primary-dark: var(--primary-700);
  --primary-light: var(--primary-400);
  --accent-color: var(--accent-500);
  --text-primary: var(--neutral-900);
  --text-secondary: var(--neutral-600);
  --text-light: var(--neutral-400);
  --background: #ffffff;
  --background-secondary: var(--neutral-50);
  --background-tertiary: var(--primary-50);
  --border-color: var(--neutral-200);
  --border-light: var(--neutral-100);

  /* Advanced Shadows */
  --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);
  --shadow-glow: 0 0 20px rgba(59, 130, 246, 0.3);
  --shadow-colored: 0 10px 25px -5px rgba(59, 130, 246, 0.2);

  /* Typography System */
  --font-primary: 'NewBlackTypeface', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-display: 'NewBlackTypeface', Georgia, serif;
  --font-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;

  /* Advanced Spacing Scale */
  --space-px: 1px;
  --space-0: 0;
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.25rem;
  --space-6: 1.5rem;
  --space-8: 2rem;
  --space-10: 2.5rem;
  --space-12: 3rem;
  --space-16: 4rem;
  --space-20: 5rem;
  --space-24: 6rem;
  --space-32: 8rem;
  --space-40: 10rem;
  --space-48: 12rem;
  --space-56: 14rem;
  --space-64: 16rem;

  /* Layout */
  --container-max-width: 1400px;
  --container-padding: var(--space-6);
  --section-padding: var(--space-24) 0;
  --section-padding-sm: var(--space-16) 0;

  /* Advanced Transitions */
  --transition-all: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-fast: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-medium: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-bounce: all 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  --transition-elastic: all 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275);

  /* Border Radius Scale */
  --radius-none: 0;
  --radius-sm: 0.125rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  --radius-2xl: 1rem;
  --radius-3xl: 1.5rem;
  --radius-full: 9999px;

  /* Z-Index Scale */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;

  /* Animation Durations */
  --duration-75: 75ms;
  --duration-100: 100ms;
  --duration-150: 150ms;
  --duration-200: 200ms;
  --duration-300: 300ms;
  --duration-500: 500ms;
  --duration-700: 700ms;
  --duration-1000: 1000ms;
}

/* ===== RESET & BASE STYLES ===== */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  font-size: 16px;
}

body {
  font-family: var(--font-primary);
  line-height: 1.6;
  color: var(--text-primary);
  background-color: var(--background);
  overflow-x: hidden;
}

/* ===== TYPOGRAPHY ===== */
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-display);
  font-weight: 600;
  line-height: 1.2;
  margin-bottom: 1rem;
}

h1 { font-size: clamp(2.5rem, 5vw, 4rem); }
h2 { font-size: clamp(2rem, 4vw, 3rem); }
h3 { font-size: clamp(1.5rem, 3vw, 2rem); }
h4 { font-size: clamp(1.25rem, 2.5vw, 1.5rem); }

p {
  margin-bottom: 1rem;
  color: var(--text-secondary);
}

a {
  text-decoration: none;
  color: inherit;
  transition: var(--transition-fast);
}

/* ===== UTILITY CLASSES ===== */
.container {
  max-width: var(--container-max-width);
  margin: 0 auto;
  padding: 0 1rem;
}

.btn {
  display: inline-block;
  padding: 0.875rem 2rem;
  border-radius: var(--radius-medium);
  font-weight: 500;
  text-align: center;
  transition: var(--transition-medium);
  cursor: pointer;
  border: none;
  font-size: 1rem;
  text-decoration: none;
}

.btn-primary {
  background-color: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background-color: var(--primary-dark);
  transform: translateY(-2px);
  box-shadow: var(--shadow-medium);
}

.btn-secondary {
  background-color: transparent;
  color: var(--primary-color);
  border: 2px solid var(--primary-color);
}

.btn-secondary:hover {
  background-color: var(--primary-color);
  color: white;
  transform: translateY(-2px);
}

/* ===== STUNNING LOADING SCREEN ===== */
.loader-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, var(--primary-50) 0%, var(--background) 50%, var(--accent-50) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-toast);
  overflow: hidden;
}

.loader-container {
  position: relative;
  text-align: center;
  z-index: 2;
}

/* Animated Brand Text */
.loader-brand {
  margin-bottom: var(--space-16);
}

.brand-text {
  display: flex;
  justify-content: center;
  gap: var(--space-2);
}

.brand-letter {
  font-family: var(--font-display);
  font-size: clamp(3rem, 8vw, 6rem);
  font-weight: 800;
  color: var(--primary-600);
  opacity: 0;
  transform: translateY(100px) rotateX(90deg);
  animation: letterReveal 0.8s cubic-bezier(0.175, 0.885, 0.32, 1.275) forwards;
  text-shadow: 0 10px 30px rgba(59, 130, 246, 0.3);
}

.brand-letter:nth-child(1) { animation-delay: 0.1s; }
.brand-letter:nth-child(2) { animation-delay: 0.2s; }
.brand-letter:nth-child(3) { animation-delay: 0.3s; }
.brand-letter:nth-child(4) { animation-delay: 0.4s; }
.brand-letter:nth-child(5) { animation-delay: 0.5s; }
.brand-letter:nth-child(6) { animation-delay: 0.6s; }
.brand-letter:nth-child(7) { animation-delay: 0.7s; }
.brand-letter:nth-child(8) { animation-delay: 0.8s; }
.brand-letter:nth-child(9) { animation-delay: 0.9s; }

@keyframes letterReveal {
  to {
    opacity: 1;
    transform: translateY(0) rotateX(0);
  }
}

/* Geometric Loading Animation */
.loader-geometry {
  position: relative;
  width: 200px;
  height: 200px;
  margin: var(--space-12) auto;
}

.geometry-shape {
  position: absolute;
  border-radius: var(--radius-lg);
  animation: geometryFloat 3s ease-in-out infinite;
}

.shape-1 {
  width: 60px;
  height: 60px;
  background: linear-gradient(45deg, var(--primary-400), var(--primary-600));
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  animation-delay: 0s;
  box-shadow: var(--shadow-colored);
}

.shape-2 {
  width: 40px;
  height: 40px;
  background: linear-gradient(45deg, var(--accent-400), var(--accent-600));
  top: 30%;
  right: 0;
  animation-delay: 0.6s;
  border-radius: 50%;
  box-shadow: var(--shadow-lg);
}

.shape-3 {
  width: 50px;
  height: 50px;
  background: linear-gradient(45deg, var(--primary-300), var(--primary-500));
  bottom: 0;
  right: 20%;
  animation-delay: 1.2s;
  clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
  box-shadow: var(--shadow-md);
}

.shape-4 {
  width: 35px;
  height: 35px;
  background: linear-gradient(45deg, var(--accent-300), var(--accent-500));
  bottom: 30%;
  left: 0;
  animation-delay: 1.8s;
  border-radius: 50%;
  box-shadow: var(--shadow-lg);
}

.shape-5 {
  width: 45px;
  height: 45px;
  background: linear-gradient(45deg, var(--primary-200), var(--primary-400));
  top: 20%;
  left: 10%;
  animation-delay: 2.4s;
  transform: rotate(45deg);
  box-shadow: var(--shadow-md);
}

@keyframes geometryFloat {
  0%, 100% {
    transform: translateY(0) rotate(0deg) scale(1);
  }
  33% {
    transform: translateY(-20px) rotate(120deg) scale(1.1);
  }
  66% {
    transform: translateY(10px) rotate(240deg) scale(0.9);
  }
}

/* Progress Indicator */
.loader-progress-container {
  margin-top: var(--space-16);
  width: 300px;
  margin-left: auto;
  margin-right: auto;
}

.progress-text {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-4);
  font-family: var(--font-primary);
}

.progress-label {
  font-size: 1rem;
  font-weight: 500;
  color: var(--text-secondary);
  letter-spacing: 0.5px;
}

.progress-percentage {
  font-size: 1rem;
  font-weight: 700;
  color: var(--primary-600);
  font-family: var(--font-mono);
}

.progress-bar-container {
  position: relative;
  height: 6px;
  background: var(--neutral-200);
  border-radius: var(--radius-full);
  overflow: hidden;
}

.progress-bar-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, var(--neutral-100), var(--neutral-200));
}

.progress-bar-fill {
  position: absolute;
  top: 0;
  left: 0;
  width: 0%;
  height: 100%;
  background: linear-gradient(90deg, var(--primary-400), var(--primary-600), var(--accent-500));
  border-radius: var(--radius-full);
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.progress-bar-glow {
  position: absolute;
  top: -2px;
  left: 0;
  width: 0%;
  height: 10px;
  background: linear-gradient(90deg, transparent, var(--primary-400), transparent);
  border-radius: var(--radius-full);
  filter: blur(4px);
  opacity: 0.7;
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Particle System */
.loader-particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  overflow: hidden;
}

.particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: var(--primary-400);
  border-radius: 50%;
  opacity: 0;
  animation: particleFloat 4s linear infinite;
}

.particle:nth-child(1) {
  left: 10%;
  animation-delay: 0s;
  animation-duration: 3s;
}

.particle:nth-child(2) {
  left: 20%;
  animation-delay: 0.5s;
  animation-duration: 3.5s;
  background: var(--accent-400);
}

.particle:nth-child(3) {
  left: 30%;
  animation-delay: 1s;
  animation-duration: 4s;
}

.particle:nth-child(4) {
  left: 40%;
  animation-delay: 1.5s;
  animation-duration: 3.2s;
  background: var(--primary-300);
}

.particle:nth-child(5) {
  left: 50%;
  animation-delay: 2s;
  animation-duration: 3.8s;
  background: var(--accent-300);
}

.particle:nth-child(6) {
  left: 60%;
  animation-delay: 2.5s;
  animation-duration: 3.3s;
}

.particle:nth-child(7) {
  left: 70%;
  animation-delay: 3s;
  animation-duration: 4.2s;
  background: var(--primary-200);
}

.particle:nth-child(8) {
  left: 80%;
  animation-delay: 3.5s;
  animation-duration: 3.7s;
  background: var(--accent-200);
}

.particle:nth-child(9) {
  left: 90%;
  animation-delay: 4s;
  animation-duration: 3.9s;
}

.particle:nth-child(10) {
  left: 95%;
  animation-delay: 4.5s;
  animation-duration: 3.4s;
  background: var(--primary-100);
}

@keyframes particleFloat {
  0% {
    transform: translateY(100vh) scale(0);
    opacity: 0;
  }
  10% {
    opacity: 1;
    transform: translateY(90vh) scale(1);
  }
  90% {
    opacity: 1;
    transform: translateY(-10vh) scale(1);
  }
  100% {
    transform: translateY(-20vh) scale(0);
    opacity: 0;
  }
}

/* Loading Messages */
.loader-messages {
  position: absolute;
  bottom: var(--space-20);
  left: 50%;
  transform: translateX(-50%);
  text-align: center;
}

.loading-message {
  font-family: var(--font-primary);
  font-size: 1.125rem;
  font-weight: 500;
  color: var(--text-secondary);
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  position: absolute;
  white-space: nowrap;
  left: 50%;
  transform: translateX(-50%) translateY(20px);
}

.loading-message.active {
  opacity: 1;
  transform: translateX(-50%) translateY(0);
}

/* Background Animation */
.loader-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  overflow: hidden;
}

.bg-gradient-1,
.bg-gradient-2,
.bg-gradient-3 {
  position: absolute;
  border-radius: 50%;
  filter: blur(60px);
  opacity: 0.3;
  animation: gradientFloat 8s ease-in-out infinite;
}

.bg-gradient-1 {
  width: 300px;
  height: 300px;
  background: linear-gradient(45deg, var(--primary-200), var(--primary-400));
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.bg-gradient-2 {
  width: 400px;
  height: 400px;
  background: linear-gradient(45deg, var(--accent-200), var(--accent-400));
  top: 50%;
  right: 10%;
  animation-delay: 2s;
}

.bg-gradient-3 {
  width: 250px;
  height: 250px;
  background: linear-gradient(45deg, var(--primary-100), var(--primary-300));
  bottom: 20%;
  left: 30%;
  animation-delay: 4s;
}

@keyframes gradientFloat {
  0%, 100% {
    transform: translate(0, 0) scale(1);
  }
  33% {
    transform: translate(30px, -30px) scale(1.1);
  }
  66% {
    transform: translate(-20px, 20px) scale(0.9);
  }
}

/* ===== NAVIGATION ===== */
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  background-color: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--border-color);
  z-index: 1000;
  transition: var(--transition-medium);
}

.nav-container {
  max-width: var(--container-max-width);
  margin: 0 auto;
  padding: 0 1rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 70px;
}

.nav-logo a {
  font-family: var(--font-display);
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary);
}

.nav-menu {
  display: flex;
  list-style: none;
  gap: 2rem;
}

.nav-link {
  color: var(--text-secondary);
  font-weight: 500;
  position: relative;
}

.nav-link:hover,
.nav-link.active {
  color: var(--primary-color);
}

.nav-link::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 0;
  height: 2px;
  background-color: var(--primary-color);
  transition: var(--transition-medium);
}

.nav-link:hover::after,
.nav-link.active::after {
  width: 100%;
}

/* Hamburger Menu */
.hamburger {
  display: none;
  flex-direction: column;
  cursor: pointer;
}

.bar {
  width: 25px;
  height: 3px;
  background-color: var(--text-primary);
  margin: 3px 0;
  transition: var(--transition-fast);
}

/* ===== HERO SECTION ===== */
.hero {
  min-height: 100vh;
  display: flex;
  align-items: center;
  position: relative;
  padding-top: 70px;
}

.hero-container {
  max-width: var(--container-max-width);
  margin: 0 auto;
  padding: 0 1rem;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
}

.hero-content {
  z-index: 2;
}

.hero-title {
  margin-bottom: 2rem;
}

.hero-greeting {
  display: block;
  font-size: 1.25rem;
  color: var(--text-secondary);
  font-weight: 400;
  margin-bottom: 0.5rem;
}

.hero-name {
  display: block;
  font-size: clamp(3rem, 6vw, 5rem);
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.hero-role {
  display: block;
  font-size: clamp(1.5rem, 3vw, 2.5rem);
  color: var(--primary-color);
  font-weight: 500;
}

.hero-description {
  font-size: 1.125rem;
  margin-bottom: 2.5rem;
  max-width: 500px;
}

.hero-buttons {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.hero-visual {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.hero-image {
  width: 400px;
  height: 400px;
  position: relative;
}

.hero-shape {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  border-radius: 50% 20% 50% 20%;
  position: relative;
  animation: float 6s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(5deg); }
}

.scroll-indicator {
  position: absolute;
  bottom: 2rem;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.scroll-line {
  width: 2px;
  height: 30px;
  background-color: var(--text-light);
  position: relative;
  overflow: hidden;
}

.scroll-line::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: var(--primary-color);
  animation: scroll-animation 2s infinite;
}

@keyframes scroll-animation {
  0% { transform: translateY(-100%); }
  100% { transform: translateY(100%); }
}

.scroll-text {
  font-size: 0.875rem;
  color: var(--text-light);
  writing-mode: vertical-rl;
  text-orientation: mixed;
}

/* ===== SECTION STYLES ===== */
section {
  padding: var(--section-padding);
}

.section-header {
  text-align: center;
  margin-bottom: 4rem;
}

.section-title {
  color: var(--text-primary);
  margin-bottom: 1rem;
}

.section-subtitle {
  font-size: 1.125rem;
  color: var(--text-secondary);
  max-width: 600px;
  margin: 0 auto;
}

/* ===== ABOUT SECTION ===== */
.about {
  background-color: var(--background-secondary);
}

.about-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 4rem;
  align-items: start;
}

.about-text h3 {
  color: var(--text-primary);
  margin-bottom: 1.5rem;
  font-size: 1.5rem;
}

.about-text p {
  font-size: 1.125rem;
  margin-bottom: 2rem;
}

.skills h4 {
  color: var(--text-primary);
  margin-bottom: 1.5rem;
  font-size: 1.25rem;
}

.skills-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 1rem;
}

.skill-item {
  background-color: var(--background);
  padding: 0.75rem 1rem;
  border-radius: var(--radius-medium);
  text-align: center;
  font-weight: 500;
  color: var(--text-primary);
  border: 2px solid var(--border-color);
  transition: var(--transition-medium);
}

.skill-item:hover {
  border-color: var(--primary-color);
  transform: translateY(-2px);
  box-shadow: var(--shadow-light);
}

.about-stats {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.stat-item {
  text-align: center;
  padding: 2rem;
  background-color: var(--background);
  border-radius: var(--radius-large);
  box-shadow: var(--shadow-light);
  transition: var(--transition-medium);
}

.stat-item:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-medium);
}

.stat-number {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--primary-color);
  font-family: var(--font-display);
}

.stat-label {
  color: var(--text-secondary);
  font-weight: 500;
  margin-top: 0.5rem;
}

/* ===== PORTFOLIO SECTION ===== */
.portfolio-filters {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 3rem;
  flex-wrap: wrap;
}

.filter-btn {
  padding: 0.75rem 1.5rem;
  background-color: transparent;
  border: 2px solid var(--border-color);
  border-radius: var(--radius-medium);
  color: var(--text-secondary);
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition-medium);
}

.filter-btn:hover,
.filter-btn.active {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  color: white;
}

.portfolio-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
}

.portfolio-item {
  background-color: var(--background);
  border-radius: var(--radius-large);
  overflow: hidden;
  box-shadow: var(--shadow-light);
  transition: var(--transition-medium);
  cursor: pointer;
}

.portfolio-item:hover {
  transform: translateY(-10px);
  box-shadow: var(--shadow-large);
}

.portfolio-image {
  width: 100%;
  height: 250px;
  background-color: var(--background-secondary);
  position: relative;
  overflow: hidden;
}

.portfolio-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: var(--transition-slow);
}

.portfolio-item:hover .portfolio-image img {
  transform: scale(1.1);
}

.portfolio-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(37, 99, 235, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: var(--transition-medium);
}

.portfolio-item:hover .portfolio-overlay {
  opacity: 1;
}

.portfolio-overlay-content {
  text-align: center;
  color: white;
}

.portfolio-overlay-content h4 {
  margin-bottom: 0.5rem;
}

.portfolio-content {
  padding: 1.5rem;
}

.portfolio-title {
  font-size: 1.25rem;
  margin-bottom: 0.5rem;
  color: var(--text-primary);
}

.portfolio-category {
  color: var(--primary-color);
  font-weight: 500;
  font-size: 0.875rem;
  margin-bottom: 1rem;
}

.portfolio-description {
  color: var(--text-secondary);
  font-size: 0.95rem;
  line-height: 1.6;
}

/* ===== CONTACT SECTION ===== */
.contact {
  background-color: var(--background-secondary);
}

.contact-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: start;
}

.contact-info h3 {
  color: var(--text-primary);
  margin-bottom: 1rem;
}

.contact-info p {
  font-size: 1.125rem;
  margin-bottom: 2rem;
}

.contact-details {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.contact-icon {
  width: 50px;
  height: 50px;
  background-color: var(--primary-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
}

.contact-text {
  display: flex;
  flex-direction: column;
}

.contact-text span:first-child {
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.25rem;
}

.contact-text a {
  color: var(--primary-color);
  transition: var(--transition-fast);
}

.contact-text a:hover {
  text-decoration: underline;
}

.contact-form {
  background-color: var(--background);
  padding: 2rem;
  border-radius: var(--radius-large);
  box-shadow: var(--shadow-light);
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 1rem;
  border: 2px solid var(--border-color);
  border-radius: var(--radius-medium);
  font-family: var(--font-primary);
  font-size: 1rem;
  transition: var(--transition-fast);
  background-color: var(--background);
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.form-group textarea {
  resize: vertical;
  min-height: 120px;
}

/* ===== FOOTER ===== */
.footer {
  background-color: var(--text-primary);
  color: white;
  padding: 2rem 0;
}

.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
}

.footer-text p {
  color: var(--text-light);
  margin: 0;
}

.social-links {
  display: flex;
  gap: 1.5rem;
}

.social-link {
  color: var(--text-light);
  font-weight: 500;
  transition: var(--transition-fast);
}

.social-link:hover {
  color: white;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
  /* Navigation */
  .nav-menu {
    position: fixed;
    left: -100%;
    top: 70px;
    flex-direction: column;
    background-color: var(--background);
    width: 100%;
    text-align: center;
    transition: var(--transition-medium);
    box-shadow: var(--shadow-medium);
    padding: 2rem 0;
  }

  .nav-menu.active {
    left: 0;
  }

  .nav-menu li {
    margin: 1rem 0;
  }

  .hamburger {
    display: flex;
  }

  .hamburger.active .bar:nth-child(2) {
    opacity: 0;
  }

  .hamburger.active .bar:nth-child(1) {
    transform: translateY(8px) rotate(45deg);
  }

  .hamburger.active .bar:nth-child(3) {
    transform: translateY(-8px) rotate(-45deg);
  }

  /* Hero Section */
  .hero-container {
    grid-template-columns: 1fr;
    gap: 2rem;
    text-align: center;
  }

  .hero-visual {
    order: -1;
  }

  .hero-image {
    width: 300px;
    height: 300px;
  }

  .hero-buttons {
    justify-content: center;
  }

  /* About Section */
  .about-content {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .about-stats {
    flex-direction: row;
    justify-content: space-around;
  }

  .stat-item {
    flex: 1;
    margin: 0 0.5rem;
  }

  /* Portfolio */
  .portfolio-grid {
    grid-template-columns: 1fr;
  }

  .portfolio-filters {
    justify-content: center;
  }

  .filter-btn {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
  }

  /* Contact */
  .contact-content {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  /* Footer */
  .footer-content {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }
}

@media (max-width: 480px) {
  /* Typography adjustments */
  .hero-name {
    font-size: 2.5rem;
  }

  .hero-role {
    font-size: 1.5rem;
  }

  .section-title {
    font-size: 2rem;
  }

  /* Spacing adjustments */
  :root {
    --section-padding: 3rem 0;
  }

  .container {
    padding: 0 0.75rem;
  }

  /* Hero adjustments */
  .hero-image {
    width: 250px;
    height: 250px;
  }

  .hero-buttons {
    flex-direction: column;
    align-items: center;
  }

  .btn {
    width: 100%;
    max-width: 250px;
  }

  /* About stats */
  .about-stats {
    flex-direction: column;
  }

  .stat-item {
    margin: 0;
  }

  /* Portfolio grid */
  .portfolio-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .portfolio-item {
    margin: 0 auto;
    max-width: 350px;
  }

  /* Contact form */
  .contact-form {
    padding: 1.5rem;
  }

  /* Skills grid */
  .skills-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* ===== ANIMATIONS & TRANSITIONS ===== */
.fade-in {
  opacity: 0;
  transform: translateY(30px);
}

.fade-in-left {
  opacity: 0;
  transform: translateX(-30px);
}

.fade-in-right {
  opacity: 0;
  transform: translateX(30px);
}

.scale-in {
  opacity: 0;
  transform: scale(0.8);
}

/* ===== UTILITY CLASSES ===== */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-1 { margin-bottom: 0.5rem; }
.mb-2 { margin-bottom: 1rem; }
.mb-3 { margin-bottom: 1.5rem; }
.mb-4 { margin-bottom: 2rem; }

.mt-1 { margin-top: 0.5rem; }
.mt-2 { margin-top: 1rem; }
.mt-3 { margin-top: 1.5rem; }
.mt-4 { margin-top: 2rem; }

.hidden { display: none; }
.visible { display: block; }

/* ===== SMOOTH SCROLLING ===== */
@media (prefers-reduced-motion: no-preference) {
  html {
    scroll-behavior: smooth;
  }
}
