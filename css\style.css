/* ===== CSS RESET & BASE STYLES ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Colors */
    --primary: #ffc321;
    --primary-light: #ffd454;
    --primary-dark: #e6af1d;
    --secondary: #1a1a1a;
    --accent: #6366f1;
    --text-primary: #0f0f0f;
    --text-secondary: #6b7280;
    --text-light: #9ca3af;
    --white: #ffffff;
    --black: #000000;
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-800: #1f2937;
    --gray-900: #111827;
    
    /* Gradients */
    --gradient-primary: linear-gradient(135deg, #ffc321 0%, #ffd454 100%);
    --gradient-secondary: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    --gradient-dark: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    --gradient-glass: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    
    /* Typography */
    --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    --font-display: 'Space Grotesk', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    
    /* Spacing */
    --container-max-width: 1200px;
    --section-padding: 120px 0;
    --section-padding-mobile: 80px 0;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-glass: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
    
    /* Border Radius */
    --radius-sm: 8px;
    --radius-md: 12px;
    --radius-lg: 16px;
    --radius-xl: 24px;
    --radius-full: 9999px;
    
    /* Transitions */
    --transition-fast: 0.15s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: var(--font-primary);
    font-size: 16px;
    line-height: 1.6;
    color: var(--text-primary);
    background: var(--white);
    overflow-x: hidden;
}

/* ===== UTILITY CLASSES ===== */
.container {
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 24px;
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 16px 32px;
    border: none;
    border-radius: var(--radius-full);
    font-family: var(--font-primary);
    font-size: 16px;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.btn-primary {
    background: var(--gradient-primary);
    color: var(--white);
    box-shadow: var(--shadow-lg);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
    border: 1px solid var(--gray-200);
    backdrop-filter: blur(10px);
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: var(--primary);
}

.btn-outline {
    background: transparent;
    color: var(--text-primary);
    border: 2px solid var(--gray-200);
}

.btn-outline:hover {
    border-color: var(--primary);
    background: var(--gradient-primary);
    color: var(--white);
}

.btn-full {
    width: 100%;
    justify-content: center;
}

/* ===== LOADING SCREEN ===== */
.loader-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--white);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    transition: opacity 0.5s ease;
}

.loader-content {
    text-align: center;
}

.loader-logo {
    margin-bottom: 32px;
}

.logo-img {
    height: 60px;
    width: auto;
    opacity: 0;
    animation: fadeInUp 0.8s ease forwards;
}

.loader-progress {
    width: 200px;
    height: 2px;
    background: var(--gray-200);
    border-radius: var(--radius-full);
    margin: 0 auto 24px;
    overflow: hidden;
}

.progress-line {
    height: 100%;
    background: var(--gradient-primary);
    border-radius: var(--radius-full);
    width: 0%;
    animation: progressLoad 2s ease forwards;
}

.loader-text {
    font-size: 14px;
    color: var(--text-secondary);
    opacity: 0;
    animation: fadeInUp 0.8s ease 0.3s forwards;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes progressLoad {
    to {
        width: 100%;
    }
}

/* ===== NAVIGATION ===== */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    z-index: 1000;
    transition: all var(--transition-normal);
}

.nav-container {
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 24px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 80px;
}

.nav-logo-img {
    height: 40px;
    width: auto;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 40px;
    align-items: center;
}

.nav-link {
    font-size: 15px;
    font-weight: 500;
    color: var(--text-primary);
    text-decoration: none;
    transition: color var(--transition-fast);
    position: relative;
}

.nav-link:hover {
    color: var(--primary);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -4px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--gradient-primary);
    transition: width var(--transition-normal);
}

.nav-link:hover::after {
    width: 100%;
}

.contact-btn {
    background: var(--gradient-primary);
    color: var(--white) !important;
    padding: 12px 24px;
    border-radius: var(--radius-full);
    box-shadow: var(--shadow-md);
}

.contact-btn:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-lg);
}

.contact-btn::after {
    display: none;
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 4px;
}

.bar {
    width: 25px;
    height: 3px;
    background: var(--text-primary);
    border-radius: var(--radius-full);
    transition: all var(--transition-normal);
}

/* ===== HERO SECTION ===== */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
    padding-top: 80px;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
}

.gradient-orb {
    position: absolute;
    border-radius: 50%;
    filter: blur(60px);
    opacity: 0.6;
    animation: float 6s ease-in-out infinite;
}

.orb-1 {
    width: 300px;
    height: 300px;
    background: var(--gradient-primary);
    top: 20%;
    right: 10%;
    animation-delay: 0s;
}

.orb-2 {
    width: 200px;
    height: 200px;
    background: var(--gradient-secondary);
    bottom: 30%;
    left: 15%;
    animation-delay: 2s;
}

.orb-3 {
    width: 150px;
    height: 150px;
    background: linear-gradient(135deg, #ec4899 0%, #f97316 100%);
    top: 60%;
    right: 30%;
    animation-delay: 4s;
}

.grid-pattern {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: 
        linear-gradient(rgba(0, 0, 0, 0.02) 1px, transparent 1px),
        linear-gradient(90deg, rgba(0, 0, 0, 0.02) 1px, transparent 1px);
    background-size: 50px 50px;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
    }
    50% {
        transform: translateY(-20px) rotate(180deg);
    }
}

.hero-content {
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
}

.hero-badge {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    padding: 8px 16px;
    border-radius: var(--radius-full);
    margin-bottom: 32px;
    font-size: 14px;
    color: var(--text-secondary);
}

.badge-dot {
    width: 8px;
    height: 8px;
    background: #10b981;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

.hero-title {
    font-family: var(--font-display);
    font-size: clamp(3rem, 8vw, 5rem);
    font-weight: 700;
    line-height: 1.1;
    margin-bottom: 24px;
}

.title-line {
    display: block;
}

.highlight {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-description {
    font-size: 18px;
    color: var(--text-secondary);
    margin-bottom: 48px;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.hero-stats {
    display: flex;
    justify-content: center;
    gap: 48px;
    margin-bottom: 48px;
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-family: var(--font-display);
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary);
    margin-bottom: 8px;
}

.stat-label {
    font-size: 14px;
    color: var(--text-secondary);
    font-weight: 500;
}

.hero-actions {
    display: flex;
    gap: 16px;
    justify-content: center;
    flex-wrap: wrap;
}

.scroll-indicator {
    position: absolute;
    bottom: 32px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    color: var(--text-light);
    font-size: 12px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.scroll-line {
    width: 1px;
    height: 40px;
    background: var(--text-light);
    animation: scrollMove 2s ease-in-out infinite;
}

@keyframes scrollMove {
    0%, 100% {
        transform: translateY(0);
        opacity: 0;
    }
    50% {
        transform: translateY(10px);
        opacity: 1;
    }
}

/* ===== SECTION STYLES ===== */
.section-header {
    text-align: center;
    margin-bottom: 80px;
}

.section-badge {
    display: inline-flex;
    align-items: center;
    background: rgba(255, 195, 33, 0.1);
    color: var(--primary-dark);
    padding: 8px 16px;
    border-radius: var(--radius-full);
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 16px;
}

.section-title {
    font-family: var(--font-display);
    font-size: clamp(2rem, 5vw, 3rem);
    font-weight: 700;
    margin-bottom: 16px;
    color: var(--text-primary);
}

.section-subtitle {
    font-size: 18px;
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
}

/* ===== SERVICES SECTION ===== */
.services {
    padding: var(--section-padding);
    background: var(--gray-50);
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 32px;
}

.service-card {
    background: var(--white);
    padding: 40px;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal);
    border: 1px solid var(--gray-200);
}

.service-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary);
}

.service-icon {
    margin-bottom: 24px;
}

.icon-bg {
    width: 60px;
    height: 60px;
    background: var(--gradient-primary);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
}

.service-title {
    font-family: var(--font-display);
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 16px;
    color: var(--text-primary);
}

.service-description {
    color: var(--text-secondary);
    margin-bottom: 24px;
    line-height: 1.6;
}

.service-features {
    list-style: none;
}

.service-features li {
    padding: 8px 0;
    color: var(--text-secondary);
    position: relative;
    padding-left: 20px;
}

.service-features li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: var(--primary);
    font-weight: 600;
}

/* ===== WORK SECTION ===== */
.work {
    padding: var(--section-padding);
}

.work-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 32px;
    margin-bottom: 64px;
}

.work-item {
    background: var(--white);
    border-radius: var(--radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal);
    border: 1px solid var(--gray-200);
}

.work-item:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
}

.work-image {
    width: 100%;
    height: 250px;
    background: var(--gradient-primary);
    position: relative;
    overflow: hidden;
}

.work-content {
    padding: 32px;
}

.work-title {
    font-family: var(--font-display);
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 12px;
    color: var(--text-primary);
}

.work-description {
    color: var(--text-secondary);
    margin-bottom: 20px;
    line-height: 1.6;
}

.work-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.work-tag {
    background: rgba(255, 195, 33, 0.1);
    color: var(--primary-dark);
    padding: 4px 12px;
    border-radius: var(--radius-full);
    font-size: 12px;
    font-weight: 500;
}

.work-cta {
    text-align: center;
}

/* ===== ABOUT SECTION ===== */
.about {
    padding: var(--section-padding);
    background: var(--gray-50);
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 80px;
    align-items: center;
}

.about-description {
    font-size: 18px;
    color: var(--text-secondary);
    margin-bottom: 40px;
    line-height: 1.7;
}

.about-features {
    display: flex;
    flex-direction: column;
    gap: 24px;
}

.feature-item {
    display: flex;
    align-items: flex-start;
    gap: 16px;
}

.feature-icon {
    width: 48px;
    height: 48px;
    background: var(--gradient-primary);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    flex-shrink: 0;
}

.feature-content h4 {
    font-family: var(--font-display);
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 4px;
    color: var(--text-primary);
}

.feature-content p {
    color: var(--text-secondary);
    font-size: 14px;
}

.about-visual {
    display: flex;
    justify-content: center;
}

.visual-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-xl);
    padding: 48px;
    text-align: center;
    box-shadow: var(--shadow-glass);
    max-width: 300px;
}

.card-content .card-icon {
    font-size: 48px;
    margin-bottom: 24px;
    display: block;
}

.card-content h4 {
    font-family: var(--font-display);
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 16px;
    color: var(--text-primary);
}

.card-content p {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* ===== CONTACT SECTION ===== */
.contact {
    padding: var(--section-padding);
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 80px;
    align-items: start;
}

.contact-description {
    font-size: 18px;
    color: var(--text-secondary);
    margin-bottom: 40px;
    line-height: 1.7;
}

.contact-methods {
    display: flex;
    flex-direction: column;
    gap: 24px;
}

.contact-method {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 24px;
    background: rgba(255, 255, 255, 0.5);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-lg);
    text-decoration: none;
    transition: all var(--transition-normal);
}

.contact-method:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary);
}

.method-icon {
    width: 48px;
    height: 48px;
    background: var(--gradient-primary);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    flex-shrink: 0;
}

.method-content h4 {
    font-family: var(--font-display);
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 4px;
    color: var(--text-primary);
}

.method-content p {
    color: var(--text-secondary);
    font-size: 14px;
}

.contact-form-container {
    background: rgba(255, 255, 255, 0.5);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-xl);
    padding: 48px;
    box-shadow: var(--shadow-glass);
}

.contact-form {
    display: flex;
    flex-direction: column;
    gap: 24px;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: 16px 20px;
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-lg);
    font-family: var(--font-primary);
    font-size: 16px;
    background: var(--white);
    transition: all var(--transition-normal);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(255, 195, 33, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

/* ===== FOOTER ===== */
.footer {
    background: var(--gray-900);
    color: var(--white);
    padding: 80px 0 32px;
}

.footer-content {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 80px;
    margin-bottom: 48px;
}

.footer-logo {
    height: 40px;
    width: auto;
    margin-bottom: 16px;
    filter: brightness(0) invert(1);
}

.footer-description {
    color: var(--gray-300);
    line-height: 1.6;
}

.footer-links {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 40px;
}

.footer-section h4 {
    font-family: var(--font-display);
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 16px;
    color: var(--white);
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 8px;
}

.footer-section ul li a {
    color: var(--gray-300);
    text-decoration: none;
    transition: color var(--transition-fast);
}

.footer-section ul li a:hover {
    color: var(--primary);
}

.footer-bottom {
    text-align: center;
    padding-top: 32px;
    border-top: 1px solid var(--gray-800);
    color: var(--gray-400);
    font-size: 14px;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    .nav-menu {
        display: none;
    }

    .hamburger {
        display: flex;
    }

    .hero-stats {
        flex-direction: column;
        gap: 24px;
    }

    .hero-actions {
        flex-direction: column;
        align-items: center;
    }

    .services {
        padding: var(--section-padding-mobile);
    }

    .services-grid {
        grid-template-columns: 1fr;
        gap: 24px;
    }

    .service-card {
        padding: 32px 24px;
    }

    .work {
        padding: var(--section-padding-mobile);
    }

    .work-grid {
        grid-template-columns: 1fr;
        gap: 24px;
    }

    .about {
        padding: var(--section-padding-mobile);
    }

    .about-content {
        grid-template-columns: 1fr;
        gap: 48px;
    }

    .contact {
        padding: var(--section-padding-mobile);
    }

    .contact-content {
        grid-template-columns: 1fr;
        gap: 48px;
    }

    .contact-form-container {
        padding: 32px 24px;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 48px;
    }

    .footer-links {
        grid-template-columns: 1fr;
        gap: 32px;
    }
}
