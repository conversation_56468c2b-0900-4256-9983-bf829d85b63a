/* ===== MIND-BLOWING DESIGN SYSTEM ===== */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&family=Space+Grotesk:wght@300;400;500;600;700&display=swap');

/* ===== CSS RESET ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* ===== ADVANCED CUSTOM PROPERTIES ===== */
:root {
    /* Core Brand Colors */
    --primary: #ffc321;
    --primary-glow: #ffc321;
    --secondary: #0a0a0a;
    --accent-purple: #6366f1;
    --accent-pink: #ec4899;
    --accent-cyan: #06b6d4;
    --accent-green: #10b981;
    
    /* Text Colors */
    --text-primary: #0a0a0a;
    --text-secondary: #374151;
    --text-muted: #6b7280;
    --text-inverse: #ffffff;
    
    /* Background Colors */
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-dark: #0f0f0f;
    --bg-glass: rgba(255, 255, 255, 0.1);
    
    /* Dynamic Gradients */
    --gradient-primary: linear-gradient(135deg, #ffc321 0%, #ff8a00 50%, #ffc321 100%);
    --gradient-rainbow: linear-gradient(135deg, #ffc321 0%, #ec4899 25%, #6366f1 50%, #06b6d4 75%, #10b981 100%);
    --gradient-glass: linear-gradient(135deg, rgba(255, 255, 255, 0.25) 0%, rgba(255, 255, 255, 0.1) 100%);
    --gradient-dark: linear-gradient(135deg, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0.4) 100%);
    
    /* Morphing Background Gradients */
    --bg-morph-1: radial-gradient(circle at 20% 50%, rgba(255, 195, 33, 0.15) 0%, transparent 50%),
                  radial-gradient(circle at 80% 20%, rgba(99, 102, 241, 0.15) 0%, transparent 50%),
                  radial-gradient(circle at 40% 80%, rgba(236, 72, 153, 0.15) 0%, transparent 50%);
    
    --bg-morph-2: radial-gradient(circle at 60% 30%, rgba(6, 182, 212, 0.15) 0%, transparent 50%),
                  radial-gradient(circle at 20% 80%, rgba(255, 195, 33, 0.15) 0%, transparent 50%),
                  radial-gradient(circle at 90% 70%, rgba(16, 185, 129, 0.15) 0%, transparent 50%);
    
    /* Typography System */
    --font-primary: 'Inter', system-ui, -apple-system, sans-serif;
    --font-display: 'Space Grotesk', system-ui, -apple-system, sans-serif;
    --font-mono: 'JetBrains Mono', 'Fira Code', monospace;
    
    /* Fluid Typography */
    --text-xs: clamp(0.75rem, 0.7rem + 0.25vw, 0.875rem);
    --text-sm: clamp(0.875rem, 0.8rem + 0.375vw, 1rem);
    --text-base: clamp(1rem, 0.95rem + 0.25vw, 1.125rem);
    --text-lg: clamp(1.125rem, 1.05rem + 0.375vw, 1.25rem);
    --text-xl: clamp(1.25rem, 1.15rem + 0.5vw, 1.5rem);
    --text-2xl: clamp(1.5rem, 1.35rem + 0.75vw, 2rem);
    --text-3xl: clamp(1.875rem, 1.65rem + 1.125vw, 2.5rem);
    --text-4xl: clamp(2.25rem, 1.95rem + 1.5vw, 3.5rem);
    --text-5xl: clamp(3rem, 2.5rem + 2.5vw, 5rem);
    --text-6xl: clamp(3.75rem, 3rem + 3.75vw, 6.5rem);
    --text-7xl: clamp(4.5rem, 3.5rem + 5vw, 8rem);
    --text-8xl: clamp(6rem, 4rem + 10vw, 12rem);
    
    /* Spacing System */
    --space-1: 0.25rem;
    --space-2: 0.5rem;
    --space-3: 0.75rem;
    --space-4: 1rem;
    --space-5: 1.25rem;
    --space-6: 1.5rem;
    --space-8: 2rem;
    --space-10: 2.5rem;
    --space-12: 3rem;
    --space-16: 4rem;
    --space-20: 5rem;
    --space-24: 6rem;
    --space-32: 8rem;
    --space-40: 10rem;
    --space-48: 12rem;
    --space-64: 16rem;
    
    /* Advanced Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    --shadow-glow: 0 0 20px rgba(255, 195, 33, 0.5);
    --shadow-glow-purple: 0 0 20px rgba(99, 102, 241, 0.5);
    --shadow-glow-pink: 0 0 20px rgba(236, 72, 153, 0.5);
    --shadow-glass: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
    
    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-2xl: 1.5rem;
    --radius-3xl: 2rem;
    --radius-full: 9999px;
    
    /* Transitions */
    --transition-fast: 150ms ease;
    --transition-base: 250ms ease;
    --transition-slow: 350ms ease;
    --transition-bounce: 250ms cubic-bezier(0.68, -0.55, 0.265, 1.55);
    --transition-elastic: 500ms cubic-bezier(0.68, -0.55, 0.265, 1.55);
    
    /* Z-Index Scale */
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal-backdrop: 1040;
    --z-modal: 1050;
    --z-popover: 1060;
    --z-tooltip: 1070;
    --z-toast: 1080;
}

/* ===== BASE STYLES ===== */
html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: var(--font-primary);
    font-size: var(--text-base);
    line-height: 1.6;
    color: var(--text-primary);
    background: var(--bg-primary);
    overflow-x: hidden;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* ===== SELECTION STYLES ===== */
::selection {
    background: var(--primary);
    color: var(--text-inverse);
}

::-moz-selection {
    background: var(--primary);
    color: var(--text-inverse);
}

/* ===== SCROLLBAR STYLES ===== */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
    background: var(--primary);
    border-radius: var(--radius-full);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--primary-glow);
}

/* ===== UTILITY CLASSES ===== */
.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 var(--space-6);
}

.section {
    padding: var(--space-32) 0;
}

.text-center {
    text-align: center;
}

.relative {
    position: relative;
}

.absolute {
    position: absolute;
}

.fixed {
    position: fixed;
}

.overflow-hidden {
    overflow: hidden;
}

.pointer-events-none {
    pointer-events: none;
}

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* ===== MORPHING BACKGROUND ===== */
.morphing-bg {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--bg-morph-1);
    z-index: -1;
    animation: morphBackground 20s ease-in-out infinite;
}

@keyframes morphBackground {
    0%, 100% {
        background: var(--bg-morph-1);
    }
    50% {
        background: var(--bg-morph-2);
    }
}

/* ===== FLOATING PARTICLES ===== */
.particles-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: -1;
}

.particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: var(--primary);
    border-radius: 50%;
    opacity: 0.6;
    animation: float 6s ease-in-out infinite;
}

.particle:nth-child(2n) {
    background: var(--accent-purple);
    animation-delay: -2s;
    animation-duration: 8s;
}

.particle:nth-child(3n) {
    background: var(--accent-pink);
    animation-delay: -4s;
    animation-duration: 10s;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
        opacity: 0.6;
    }
    50% {
        transform: translateY(-20px) rotate(180deg);
        opacity: 1;
    }
}

/* ===== GLASS MORPHISM EFFECTS ===== */
.glass {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: var(--shadow-glass);
}

.glass-dark {
    background: rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

/* ===== GLOW EFFECTS ===== */
.glow {
    box-shadow: var(--shadow-glow);
    transition: box-shadow var(--transition-base);
}

.glow:hover {
    box-shadow: 0 0 30px rgba(255, 195, 33, 0.8);
}

.glow-purple {
    box-shadow: var(--shadow-glow-purple);
}

.glow-pink {
    box-shadow: var(--shadow-glow-pink);
}

/* ===== GRADIENT TEXT ===== */
.gradient-text {
    background: var(--gradient-rainbow);
    background-size: 200% 200%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradientShift 3s ease-in-out infinite;
}

@keyframes gradientShift {
    0%, 100% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
}

/* ===== MORPHING SHAPES ===== */
.morph-shape {
    border-radius: 60% 40% 30% 70% / 60% 30% 70% 40%;
    animation: morphShape 8s ease-in-out infinite;
}

@keyframes morphShape {
    0%, 100% {
        border-radius: 60% 40% 30% 70% / 60% 30% 70% 40%;
    }
    50% {
        border-radius: 30% 60% 70% 40% / 50% 60% 30% 60%;
    }
}

/* ===== MINIMAL LOADING SCREEN ===== */
.loader-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--bg-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: var(--z-modal);
    transition: opacity var(--transition-slow);
}

.loader-content {
    text-align: center;
    animation: loaderPulse 2s ease-in-out infinite;
}

.loader-logo {
    margin-bottom: var(--space-6);
}

.logo-img {
    width: 80px;
    height: auto;
    filter: drop-shadow(var(--shadow-glow));
}

.loader-progress {
    width: 200px;
    height: 2px;
    background: var(--bg-secondary);
    border-radius: var(--radius-full);
    overflow: hidden;
    margin: var(--space-4) auto;
}

.progress-line {
    width: 0%;
    height: 100%;
    background: var(--gradient-primary);
    border-radius: var(--radius-full);
    animation: progressLoad 2.5s ease-out forwards;
}

.loader-text {
    font-size: var(--text-sm);
    color: var(--text-muted);
    font-weight: 500;
    letter-spacing: 0.1em;
    text-transform: uppercase;
}

@keyframes loaderPulse {
    0%, 100% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.05);
        opacity: 0.8;
    }
}

@keyframes progressLoad {
    0% {
        width: 0%;
    }
    100% {
        width: 100%;
    }
}

/* ===== NAVIGATION ===== */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    z-index: var(--z-fixed);
    transition: all var(--transition-base);
}

.nav-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 var(--space-6);
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 80px;
}

.nav-logo {
    display: flex;
    align-items: center;
}

.nav-logo-img {
    height: 40px;
    width: auto;
    transition: transform var(--transition-base);
}

.nav-logo-img:hover {
    transform: scale(1.1);
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: var(--space-8);
    align-items: center;
}

.nav-link {
    text-decoration: none;
    color: var(--text-primary);
    font-weight: 500;
    font-size: var(--text-sm);
    position: relative;
    transition: all var(--transition-base);
    padding: var(--space-2) var(--space-4);
    border-radius: var(--radius-lg);
}

.nav-link:hover {
    color: var(--primary);
    transform: translateY(-2px);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -4px;
    left: 50%;
    width: 0;
    height: 2px;
    background: var(--gradient-primary);
    transition: all var(--transition-base);
    transform: translateX(-50%);
}

.nav-link:hover::after {
    width: 100%;
}

.contact-btn {
    background: var(--gradient-primary);
    color: var(--text-inverse) !important;
    border-radius: var(--radius-full);
    padding: var(--space-3) var(--space-6) !important;
    box-shadow: var(--shadow-glow);
    transition: all var(--transition-base);
}

.contact-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 0 30px rgba(255, 195, 33, 0.8);
}

.contact-btn::after {
    display: none;
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 4px;
}

.bar {
    width: 25px;
    height: 3px;
    background: var(--text-primary);
    border-radius: var(--radius-full);
    transition: all var(--transition-base);
}

/* ===== HERO SECTION ===== */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
}

.gradient-orb {
    position: absolute;
    border-radius: 50%;
    filter: blur(40px);
    opacity: 0.6;
    animation: floatOrb 8s ease-in-out infinite;
}

.orb-1 {
    width: 300px;
    height: 300px;
    background: radial-gradient(circle, rgba(255, 195, 33, 0.4) 0%, transparent 70%);
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.orb-2 {
    width: 400px;
    height: 400px;
    background: radial-gradient(circle, rgba(99, 102, 241, 0.4) 0%, transparent 70%);
    top: 60%;
    right: 10%;
    animation-delay: -3s;
}

.orb-3 {
    width: 250px;
    height: 250px;
    background: radial-gradient(circle, rgba(236, 72, 153, 0.4) 0%, transparent 70%);
    top: 10%;
    right: 30%;
    animation-delay: -6s;
}

@keyframes floatOrb {
    0%, 100% {
        transform: translate(0, 0) scale(1);
    }
    33% {
        transform: translate(30px, -30px) scale(1.1);
    }
    66% {
        transform: translate(-20px, 20px) scale(0.9);
    }
}

.grid-pattern {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        linear-gradient(rgba(255, 195, 33, 0.1) 1px, transparent 1px),
        linear-gradient(90deg, rgba(255, 195, 33, 0.1) 1px, transparent 1px);
    background-size: 50px 50px;
    opacity: 0.3;
    animation: gridMove 20s linear infinite;
}

@keyframes gridMove {
    0% {
        transform: translate(0, 0);
    }
    100% {
        transform: translate(50px, 50px);
    }
}

.hero-content {
    position: relative;
    z-index: 1;
    text-align: center;
    max-width: 900px;
    margin: 0 auto;
}

.hero-title {
    font-family: var(--font-display);
    font-size: var(--text-7xl);
    font-weight: 700;
    line-height: 1.1;
    margin-bottom: var(--space-6);
    position: relative;
}

.hero-title-main {
    display: block;
    color: var(--text-primary);
    margin-bottom: var(--space-2);
}

.hero-title-highlight {
    display: block;
    background: var(--gradient-rainbow);
    background-size: 200% 200%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradientShift 3s ease-in-out infinite;
    position: relative;
}

.morphing-word {
    position: relative;
    display: inline-block;
}

.morphing-word::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-primary);
    opacity: 0;
    border-radius: var(--radius-lg);
    animation: wordMorph 4s ease-in-out infinite;
}

@keyframes wordMorph {
    0%, 100% {
        opacity: 0;
        transform: scale(1);
    }
    50% {
        opacity: 0.1;
        transform: scale(1.05);
    }
}

.hero-subtitle {
    font-size: var(--text-xl);
    color: var(--text-secondary);
    margin-bottom: var(--space-8);
    line-height: 1.6;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.hero-cta {
    display: flex;
    gap: var(--space-4);
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    margin-bottom: var(--space-12);
}

.cta-primary {
    background: var(--gradient-primary);
    color: var(--text-inverse);
    text-decoration: none;
    padding: var(--space-4) var(--space-8);
    border-radius: var(--radius-full);
    font-weight: 600;
    font-size: var(--text-lg);
    box-shadow: var(--shadow-glow);
    transition: all var(--transition-base);
    position: relative;
    overflow: hidden;
}

.cta-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.cta-primary:hover::before {
    left: 100%;
}

.cta-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 0 40px rgba(255, 195, 33, 0.8);
}

.cta-secondary {
    background: transparent;
    color: var(--text-primary);
    text-decoration: none;
    padding: var(--space-4) var(--space-8);
    border: 2px solid var(--text-primary);
    border-radius: var(--radius-full);
    font-weight: 600;
    font-size: var(--text-lg);
    transition: all var(--transition-base);
}

.cta-secondary:hover {
    background: var(--text-primary);
    color: var(--text-inverse);
    transform: translateY(-3px);
}

/* ===== HERO STATS ===== */
.hero-stats {
    display: flex;
    justify-content: center;
    gap: var(--space-12);
    margin-top: var(--space-16);
    flex-wrap: wrap;
}

.stat-item {
    text-align: center;
    min-width: 120px;
}

.stat-number {
    font-family: var(--font-display);
    font-size: var(--text-4xl);
    font-weight: 700;
    line-height: 1;
    margin-bottom: var(--space-2);
}

.stat-label {
    font-size: var(--text-sm);
    color: var(--text-secondary);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* ===== SCROLL INDICATOR ===== */
.scroll-indicator {
    position: absolute;
    bottom: var(--space-8);
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--space-2);
    animation: scrollBounce 2s ease-in-out infinite;
}

.scroll-line {
    width: 2px;
    height: 40px;
    background: var(--gradient-primary);
    border-radius: var(--radius-full);
    position: relative;
    overflow: hidden;
}

.scroll-line::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 20px;
    background: var(--text-inverse);
    border-radius: var(--radius-full);
    animation: scrollMove 2s ease-in-out infinite;
}

.scroll-text {
    font-size: var(--text-xs);
    color: var(--text-muted);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.1em;
}

@keyframes scrollBounce {
    0%, 100% {
        transform: translateX(-50%) translateY(0);
    }
    50% {
        transform: translateX(-50%) translateY(-10px);
    }
}

@keyframes scrollMove {
    0% {
        top: 0;
        opacity: 1;
    }
    100% {
        top: 20px;
        opacity: 0;
    }
}

/* ===== SECTION STYLES ===== */
.section-header {
    text-align: center;
    margin-bottom: var(--space-16);
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

.section-title {
    font-family: var(--font-display);
    font-size: var(--text-5xl);
    font-weight: 700;
    line-height: 1.1;
    margin-bottom: var(--space-6);
}

.section-subtitle {
    font-size: var(--text-lg);
    color: var(--text-secondary);
    line-height: 1.6;
    max-width: 600px;
    margin: 0 auto;
}

/* ===== SERVICES SECTION ===== */
.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--space-8);
    margin-top: var(--space-16);
}

.service-card {
    padding: var(--space-8);
    border-radius: var(--radius-2xl);
    transition: all var(--transition-base);
    position: relative;
    overflow: hidden;
}

.service-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-rainbow);
    opacity: 0;
    transition: opacity var(--transition-base);
    z-index: -1;
}

.service-card:hover::before {
    opacity: 0.05;
}

.service-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-2xl);
}

.service-icon {
    width: 60px;
    height: 60px;
    margin-bottom: var(--space-6);
    position: relative;
}

.icon-svg {
    width: 100%;
    height: 100%;
    color: var(--primary);
    transition: all var(--transition-base);
}

.service-card:hover .icon-svg {
    color: var(--accent-purple);
    transform: scale(1.1);
}

.service-title {
    font-family: var(--font-display);
    font-size: var(--text-xl);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--space-4);
    line-height: 1.3;
}

.service-description {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: var(--space-6);
}

.service-features {
    display: flex;
    flex-wrap: wrap;
    gap: var(--space-2);
}

.feature-tag {
    background: rgba(255, 195, 33, 0.1);
    color: var(--primary);
    padding: var(--space-1) var(--space-3);
    border-radius: var(--radius-full);
    font-size: var(--text-xs);
    font-weight: 500;
    border: 1px solid rgba(255, 195, 33, 0.2);
    transition: all var(--transition-base);
}

.feature-tag:hover {
    background: var(--primary);
    color: var(--text-inverse);
    transform: translateY(-2px);
}

/* ===== WORK SECTION ===== */
.work-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--space-8);
    margin-top: var(--space-16);
}

.work-item {
    border-radius: var(--radius-2xl);
    overflow: hidden;
    position: relative;
    transition: all var(--transition-base);
    cursor: pointer;
}

.work-item:hover {
    transform: translateY(-12px) scale(1.02);
    box-shadow: var(--shadow-2xl);
}

.work-image {
    position: relative;
    width: 100%;
    height: 300px;
    overflow: hidden;
}

.work-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.placeholder-svg {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.work-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        135deg,
        rgba(0, 0, 0, 0.8) 0%,
        rgba(0, 0, 0, 0.4) 100%
    );
    display: flex;
    align-items: flex-end;
    padding: var(--space-6);
    opacity: 0;
    transition: all var(--transition-base);
}

.work-item:hover .work-overlay {
    opacity: 1;
}

.work-info {
    color: white;
    width: 100%;
}

.work-title {
    font-family: var(--font-display);
    font-size: var(--text-xl);
    font-weight: 600;
    margin-bottom: var(--space-2);
    line-height: 1.3;
}

.work-description {
    font-size: var(--text-sm);
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: var(--space-4);
    line-height: 1.5;
}

.work-tags {
    display: flex;
    flex-wrap: wrap;
    gap: var(--space-2);
}

.work-tag {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    padding: var(--space-1) var(--space-3);
    border-radius: var(--radius-full);
    font-size: var(--text-xs);
    font-weight: 500;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.work-cta {
    text-align: center;
    margin-top: var(--space-16);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    .hero-title {
        font-size: var(--text-4xl);
    }

    .hero-subtitle {
        font-size: var(--text-base);
    }

    .hero-stats {
        gap: var(--space-6);
    }

    .services-grid {
        grid-template-columns: 1fr;
        gap: var(--space-6);
    }

    .work-grid {
        grid-template-columns: 1fr;
        gap: var(--space-6);
    }

    .section-title {
        font-size: var(--text-3xl);
    }

    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background: var(--glass-bg);
        backdrop-filter: blur(20px);
        width: 100%;
        text-align: center;
        transition: 0.3s;
        box-shadow: var(--shadow-lg);
        border-top: 1px solid var(--border-color);
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-menu li {
        margin: var(--space-4) 0;
    }

    .hamburger {
        display: block;
        cursor: pointer;
    }

    .hamburger.active .bar:nth-child(2) {
        opacity: 0;
    }

    .hamburger.active .bar:nth-child(1) {
        transform: translateY(8px) rotate(45deg);
    }

    .hamburger.active .bar:nth-child(3) {
        transform: translateY(-8px) rotate(-45deg);
    }
}

@media (max-width: 480px) {
    .hero-cta {
        flex-direction: column;
        gap: var(--space-4);
    }

    .cta-primary,
    .cta-secondary {
        width: 100%;
        justify-content: center;
    }

    .hero-stats {
        flex-direction: column;
        gap: var(--space-4);
    }
}

/* ===== ABOUT SECTION ===== */
.about-content {
    margin-top: var(--space-16);
}

.about-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--space-8);
    margin-bottom: var(--space-16);
}

.stat-card {
    text-align: center;
    padding: var(--space-8);
    border-radius: var(--radius-2xl);
    transition: all var(--transition-base);
}

.stat-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-2xl);
}

.stat-icon {
    font-size: var(--text-4xl);
    margin-bottom: var(--space-4);
}

.stat-value {
    font-family: var(--font-display);
    font-size: var(--text-4xl);
    font-weight: 700;
    line-height: 1;
    margin-bottom: var(--space-2);
}

.stat-text {
    font-size: var(--text-sm);
    color: var(--text-secondary);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* ===== CONTACT SECTION ===== */
.contact-cta {
    display: flex;
    justify-content: center;
    gap: var(--space-6);
    margin-top: var(--space-16);
    flex-wrap: wrap;
}

/* ===== ADDITIONAL ANIMATIONS ===== */
@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
    }
    33% {
        transform: translateY(-10px) rotate(120deg);
    }
    66% {
        transform: translateY(5px) rotate(240deg);
    }
}

@keyframes gradientShift {
    0%, 100% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
}

@keyframes morphBackground {
    0%, 100% {
        background: var(--bg-morph-1);
        border-radius: 60% 40% 30% 70% / 60% 30% 70% 40%;
    }
    25% {
        background: var(--bg-morph-2);
        border-radius: 30% 60% 70% 40% / 50% 60% 30% 60%;
    }
    50% {
        background: var(--bg-morph-3);
        border-radius: 50% 60% 30% 60% / 60% 40% 60% 30%;
    }
    75% {
        background: var(--bg-morph-4);
        border-radius: 60% 40% 30% 70% / 60% 30% 70% 40%;
    }
}

@keyframes borderMorph {
    0%, 100% {
        border-radius: 60% 40% 30% 70% / 60% 30% 70% 40%;
    }
    25% {
        border-radius: 30% 60% 70% 40% / 50% 60% 30% 60%;
    }
    50% {
        border-radius: 50% 60% 30% 60% / 60% 40% 60% 30%;
    }
    75% {
        border-radius: 60% 40% 30% 70% / 60% 30% 70% 40%;
    }
}

@keyframes progressLoad {
    0% {
        width: 0%;
    }
    100% {
        width: 100%;
    }
}

@keyframes logoGlow {
    0%, 100% {
        filter: drop-shadow(0 0 20px rgba(255, 195, 33, 0.3));
    }
    50% {
        filter: drop-shadow(0 0 40px rgba(255, 195, 33, 0.6));
    }
}

/* ===== UTILITY CLASSES ===== */
.text-center {
    text-align: center;
}

.mb-0 { margin-bottom: 0; }
.mb-2 { margin-bottom: var(--space-2); }
.mb-4 { margin-bottom: var(--space-4); }
.mb-6 { margin-bottom: var(--space-6); }
.mb-8 { margin-bottom: var(--space-8); }

.mt-0 { margin-top: 0; }
.mt-2 { margin-top: var(--space-2); }
.mt-4 { margin-top: var(--space-4); }
.mt-6 { margin-top: var(--space-6); }
.mt-8 { margin-top: var(--space-8); }

.opacity-0 { opacity: 0; }
.opacity-50 { opacity: 0.5; }
.opacity-100 { opacity: 1; }

.pointer-events-none { pointer-events: none; }
.cursor-pointer { cursor: pointer; }
