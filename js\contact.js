// ===== CONTACT PAGE SPECIFIC JAVASCRIPT =====

// FAQ data
const faqData = [
    {
        question: "What's your typical project timeline?",
        answer: "Project timelines vary depending on scope and complexity. A simple website might take 2-4 weeks, while a complex web application could take 2-6 months. I'll provide a detailed timeline during our initial consultation."
    },
    {
        question: "Do you work with clients remotely?",
        answer: "Yes! I work with clients worldwide. I use modern collaboration tools and maintain regular communication to ensure smooth project delivery regardless of location."
    },
    {
        question: "What's included in your design process?",
        answer: "My process includes discovery & research, wireframing, visual design, prototyping, development, testing, and launch. I also provide ongoing support and maintenance options."
    },
    {
        question: "Can you help with existing projects?",
        answer: "Absolutely! I can help improve existing websites, add new features, fix issues, or completely redesign your current digital presence."
    },
    {
        question: "What technologies do you specialize in?",
        answer: "I specialize in modern web technologies including React, Vue.js, Node.js, and various design tools like Figma and Adobe Creative Suite. I stay updated with the latest trends and best practices."
    },
    {
        question: "Do you provide ongoing support?",
        answer: "Yes, I offer various support packages including maintenance, updates, hosting assistance, and feature additions. We can discuss the best support plan for your needs."
    }
];

// Initialize contact page
document.addEventListener('DOMContentLoaded', function() {
    initContactPage();
});

function initContactPage() {
    // Initialize enhanced loader
    initEnhancedLoader();
    
    // Initialize contact animations
    initContactAnimations();
    
    // Initialize contact form
    initContactForm();
    
    // Render FAQ section
    renderFAQSection();
    
    // Initialize scroll animations
    initScrollAnimations();
}

// Enhanced loader for contact page
function initEnhancedLoader() {
    const loader = document.querySelector('.loader-overlay');
    const progressFill = document.querySelector('.progress-bar-fill');
    const progressGlow = document.querySelector('.progress-bar-glow');
    const progressPercentage = document.querySelector('.progress-percentage');
    const loadingMessages = document.querySelectorAll('.loading-message');
    
    let progress = 0;
    let messageIndex = 0;
    
    const progressInterval = setInterval(() => {
        progress += Math.random() * 12 + 8;
        
        if (progress >= 100) {
            progress = 100;
            clearInterval(progressInterval);
            
            // Complete loading
            setTimeout(() => {
                gsap.to(loader, {
                    opacity: 0,
                    duration: 1,
                    ease: 'power2.inOut',
                    onComplete: () => {
                        loader.style.display = 'none';
                        startContactAnimations();
                    }
                });
            }, 500);
        }
        
        // Update progress bar
        progressFill.style.width = progress + '%';
        progressGlow.style.width = progress + '%';
        progressPercentage.textContent = Math.round(progress) + '%';
        
        // Update loading messages
        if (progress > 20 && messageIndex === 0) {
            updateLoadingMessage(1);
            messageIndex = 1;
        } else if (progress > 45 && messageIndex === 1) {
            updateLoadingMessage(2);
            messageIndex = 2;
        } else if (progress > 70 && messageIndex === 2) {
            updateLoadingMessage(3);
            messageIndex = 3;
        } else if (progress > 90 && messageIndex === 3) {
            updateLoadingMessage(4);
            messageIndex = 4;
        }
    }, 120);
    
    function updateLoadingMessage(index) {
        loadingMessages.forEach(msg => msg.classList.remove('active'));
        if (loadingMessages[index]) {
            loadingMessages[index].classList.add('active');
        }
    }
}

// Contact animations
function initContactAnimations() {
    // Set initial states
    gsap.set('.fade-in', { opacity: 0, y: 30 });
    gsap.set('.fade-in-up', { opacity: 0, y: 50 });
    gsap.set('.fade-in-left', { opacity: 0, x: -50 });
    gsap.set('.fade-in-right', { opacity: 0, x: 50 });
}

function startContactAnimations() {
    // Hero section animations
    const heroTimeline = gsap.timeline();
    
    heroTimeline
        .to('.hero-badge', { opacity: 1, y: 0, duration: 0.8, ease: 'power2.out' })
        .to('.title-line', { 
            opacity: 1, 
            y: 0, 
            duration: 0.8, 
            stagger: 0.2, 
            ease: 'power2.out' 
        }, '-=0.4')
        .to('.hero-description', { opacity: 1, y: 0, duration: 0.8, ease: 'power2.out' }, '-=0.4')
        .to('.hero-features .feature-item', { 
            opacity: 1, 
            y: 0, 
            duration: 0.6, 
            stagger: 0.1, 
            ease: 'back.out(1.7)' 
        }, '-=0.2');
}

// Contact form functionality
function initContactForm() {
    const form = document.getElementById('contactForm');
    const submitBtn = form.querySelector('.submit-btn');
    
    // Add input animations
    const inputs = form.querySelectorAll('.form-input, .form-select, .form-textarea');
    inputs.forEach(input => {
        input.addEventListener('focus', () => {
            gsap.to(input.parentElement.querySelector('.form-label'), {
                color: 'var(--primary-600)',
                scale: 0.95,
                duration: 0.3
            });
        });
        
        input.addEventListener('blur', () => {
            if (!input.value) {
                gsap.to(input.parentElement.querySelector('.form-label'), {
                    color: 'var(--text-primary)',
                    scale: 1,
                    duration: 0.3
                });
            }
        });
    });
    
    // Form submission
    form.addEventListener('submit', async (e) => {
        e.preventDefault();
        
        // Validate form
        if (!validateForm(form)) {
            return;
        }
        
        // Show loading state
        submitBtn.classList.add('loading');
        
        // Simulate form submission (replace with actual submission logic)
        try {
            await simulateFormSubmission(new FormData(form));
            
            // Show success
            showSuccessModal();
            
            // Reset form
            form.reset();
            
        } catch (error) {
            console.error('Form submission error:', error);
            alert('There was an error sending your message. Please try again.');
        } finally {
            // Remove loading state
            submitBtn.classList.remove('loading');
        }
    });
}

// Form validation
function validateForm(form) {
    const requiredFields = form.querySelectorAll('[required]');
    let isValid = true;
    
    requiredFields.forEach(field => {
        if (!field.value.trim()) {
            showFieldError(field, 'This field is required');
            isValid = false;
        } else {
            clearFieldError(field);
        }
    });
    
    // Email validation
    const emailField = form.querySelector('#email');
    if (emailField.value && !isValidEmail(emailField.value)) {
        showFieldError(emailField, 'Please enter a valid email address');
        isValid = false;
    }
    
    return isValid;
}

function showFieldError(field, message) {
    // Remove existing error
    clearFieldError(field);
    
    // Add error styling
    field.style.borderColor = 'var(--error-color, #ef4444)';
    
    // Create error message
    const errorDiv = document.createElement('div');
    errorDiv.className = 'field-error';
    errorDiv.textContent = message;
    errorDiv.style.color = 'var(--error-color, #ef4444)';
    errorDiv.style.fontSize = '0.875rem';
    errorDiv.style.marginTop = 'var(--space-1)';
    
    field.parentElement.appendChild(errorDiv);
    
    // Animate error
    gsap.fromTo(errorDiv, 
        { opacity: 0, y: -10 },
        { opacity: 1, y: 0, duration: 0.3 }
    );
}

function clearFieldError(field) {
    field.style.borderColor = '';
    const existingError = field.parentElement.querySelector('.field-error');
    if (existingError) {
        existingError.remove();
    }
}

function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// Simulate form submission (replace with actual API call)
function simulateFormSubmission(formData) {
    return new Promise((resolve) => {
        setTimeout(() => {
            console.log('Form data:', Object.fromEntries(formData));
            resolve();
        }, 2000);
    });
}

// Success modal
function showSuccessModal() {
    const modal = document.getElementById('successModal');
    modal.style.display = 'flex';
    
    // Animate modal
    gsap.fromTo(modal, 
        { opacity: 0 },
        { opacity: 1, duration: 0.3 }
    );
    
    gsap.fromTo(modal.querySelector('.modal-content'), 
        { scale: 0.8, y: 50 },
        { scale: 1, y: 0, duration: 0.5, ease: 'back.out(1.7)' }
    );
}

function closeSuccessModal() {
    const modal = document.getElementById('successModal');
    
    gsap.to(modal, {
        opacity: 0,
        duration: 0.3,
        onComplete: () => {
            modal.style.display = 'none';
        }
    });
}

// Render FAQ section
function renderFAQSection() {
    const faqGrid = document.querySelector('.faq-grid');
    faqGrid.innerHTML = '';
    
    faqData.forEach((faq, index) => {
        const faqItem = createFAQItem(faq, index);
        faqGrid.appendChild(faqItem);
    });
}

function createFAQItem(faq, index) {
    const faqItem = document.createElement('div');
    faqItem.className = 'faq-item';
    faqItem.innerHTML = `
        <button class="faq-question">
            <span>${faq.question}</span>
            <span class="faq-icon">+</span>
        </button>
        <div class="faq-answer">
            <p>${faq.answer}</p>
        </div>
    `;
    
    // Add click event
    const questionBtn = faqItem.querySelector('.faq-question');
    questionBtn.addEventListener('click', () => {
        toggleFAQItem(faqItem);
    });
    
    // Add animation
    gsap.from(faqItem, {
        opacity: 0,
        y: 20,
        duration: 0.6,
        delay: index * 0.1,
        ease: 'power2.out',
        scrollTrigger: {
            trigger: faqItem,
            start: 'top 85%',
            toggleActions: 'play none none reverse'
        }
    });
    
    return faqItem;
}

function toggleFAQItem(faqItem) {
    const isActive = faqItem.classList.contains('active');
    
    // Close all other FAQ items
    document.querySelectorAll('.faq-item.active').forEach(item => {
        if (item !== faqItem) {
            item.classList.remove('active');
        }
    });
    
    // Toggle current item
    faqItem.classList.toggle('active');
}

// Scroll animations
function initScrollAnimations() {
    // Fade in animations
    gsap.utils.toArray('.fade-in').forEach(element => {
        gsap.to(element, {
            opacity: 1,
            y: 0,
            duration: 0.8,
            ease: 'power2.out',
            scrollTrigger: {
                trigger: element,
                start: 'top 80%',
                toggleActions: 'play none none reverse'
            }
        });
    });
    
    // Fade in up animations
    gsap.utils.toArray('.fade-in-up').forEach(element => {
        gsap.to(element, {
            opacity: 1,
            y: 0,
            duration: 0.8,
            ease: 'power2.out',
            scrollTrigger: {
                trigger: element,
                start: 'top 80%',
                toggleActions: 'play none none reverse'
            }
        });
    });
    
    // Fade in left animations
    gsap.utils.toArray('.fade-in-left').forEach(element => {
        gsap.to(element, {
            opacity: 1,
            x: 0,
            duration: 0.8,
            ease: 'power2.out',
            scrollTrigger: {
                trigger: element,
                start: 'top 80%',
                toggleActions: 'play none none reverse'
            }
        });
    });
    
    // Fade in right animations
    gsap.utils.toArray('.fade-in-right').forEach(element => {
        gsap.to(element, {
            opacity: 1,
            x: 0,
            duration: 0.8,
            ease: 'power2.out',
            scrollTrigger: {
                trigger: element,
                start: 'top 80%',
                toggleActions: 'play none none reverse'
            }
        });
    });
}

// Make closeSuccessModal available globally
window.closeSuccessModal = closeSuccessModal;
