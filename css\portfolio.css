/* ===== PORTFOLIO PAGE SPECIFIC STYLES ===== */

/* Portfolio Hero Section */
.portfolio-hero {
  min-height: 100vh;
  display: flex;
  align-items: center;
  position: relative;
  padding-top: 70px;
  overflow: hidden;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.hero-gradient {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, 
    var(--primary-50) 0%, 
    var(--background) 30%, 
    var(--accent-50) 70%, 
    var(--primary-100) 100%);
  opacity: 0.8;
}

.hero-pattern {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: 
    radial-gradient(circle at 25% 25%, var(--primary-100) 2px, transparent 2px),
    radial-gradient(circle at 75% 75%, var(--accent-100) 1px, transparent 1px);
  background-size: 60px 60px, 40px 40px;
  opacity: 0.3;
  animation: patternFloat 20s linear infinite;
}

@keyframes patternFloat {
  0% { transform: translate(0, 0); }
  100% { transform: translate(60px, 60px); }
}

.hero-content {
  position: relative;
  z-index: 2;
  text-align: center;
  max-width: 800px;
  margin: 0 auto;
}

.hero-badge {
  display: inline-block;
  padding: var(--space-2) var(--space-6);
  background: linear-gradient(135deg, var(--primary-100), var(--primary-200));
  border: 1px solid var(--primary-300);
  border-radius: var(--radius-full);
  margin-bottom: var(--space-8);
  backdrop-filter: blur(10px);
}

.badge-text {
  font-family: var(--font-primary);
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--primary-700);
  letter-spacing: 0.5px;
  text-transform: uppercase;
}

.hero-title {
  margin-bottom: var(--space-8);
  line-height: 1.1;
}

.title-line {
  display: block;
  font-family: var(--font-display);
  font-size: clamp(3rem, 8vw, 6rem);
  font-weight: 800;
  color: var(--text-primary);
  margin-bottom: var(--space-2);
}

.title-line.highlight {
  background: linear-gradient(135deg, var(--primary-600), var(--accent-500));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  position: relative;
}

.hero-description {
  font-size: 1.25rem;
  color: var(--text-secondary);
  margin-bottom: var(--space-12);
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.6;
}

.hero-stats {
  display: flex;
  justify-content: center;
  gap: var(--space-12);
  margin-top: var(--space-12);
}

.hero-stats .stat-item {
  text-align: center;
}

.hero-stats .stat-number {
  font-family: var(--font-display);
  font-size: 2.5rem;
  font-weight: 800;
  color: var(--primary-600);
  display: block;
  margin-bottom: var(--space-1);
}

.hero-stats .stat-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Portfolio Filters Section */
.portfolio-filters-section {
  padding: var(--space-20) 0;
  background: var(--background-secondary);
}

.filters-container {
  text-align: center;
}

.filters-title {
  font-family: var(--font-display);
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--space-8);
}

.portfolio-filters {
  display: flex;
  justify-content: center;
  gap: var(--space-4);
  flex-wrap: wrap;
}

.filter-btn {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-4) var(--space-6);
  background: var(--background);
  border: 2px solid var(--border-color);
  border-radius: var(--radius-xl);
  color: var(--text-secondary);
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition-medium);
  position: relative;
  overflow: hidden;
}

.filter-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
  transition: left 0.5s ease;
}

.filter-btn:hover::before {
  left: 100%;
}

.filter-btn:hover,
.filter-btn.active {
  background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
  border-color: var(--primary-600);
  color: white;
  transform: translateY(-2px);
  box-shadow: var(--shadow-colored);
}

.filter-icon {
  font-size: 1.25rem;
}

.filter-text {
  font-size: 0.95rem;
}

/* Portfolio Grid Section */
.portfolio-grid-section {
  padding: var(--space-24) 0;
}

.portfolio-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: var(--space-8);
  margin-top: var(--space-12);
}

.portfolio-item {
  background: var(--background);
  border-radius: var(--radius-2xl);
  overflow: hidden;
  box-shadow: var(--shadow-lg);
  transition: var(--transition-medium);
  cursor: pointer;
  position: relative;
  group: hover;
}

.portfolio-item:hover {
  transform: translateY(-10px) scale(1.02);
  box-shadow: var(--shadow-2xl);
}

.portfolio-image {
  width: 100%;
  height: 300px;
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, var(--primary-100), var(--accent-100));
}

.portfolio-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: var(--transition-slow);
}

.portfolio-item:hover .portfolio-image img {
  transform: scale(1.1);
}

.portfolio-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, 
    rgba(59, 130, 246, 0.9), 
    rgba(234, 179, 8, 0.9));
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: var(--transition-medium);
}

.portfolio-item:hover .portfolio-overlay {
  opacity: 1;
}

.portfolio-overlay-content {
  text-align: center;
  color: white;
  transform: translateY(20px);
  transition: var(--transition-medium);
}

.portfolio-item:hover .portfolio-overlay-content {
  transform: translateY(0);
}

.overlay-title {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: var(--space-2);
}

.overlay-subtitle {
  font-size: 1rem;
  opacity: 0.9;
}

.portfolio-content {
  padding: var(--space-6);
}

.portfolio-category {
  display: inline-block;
  padding: var(--space-1) var(--space-3);
  background: var(--primary-100);
  color: var(--primary-700);
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border-radius: var(--radius-full);
  margin-bottom: var(--space-3);
}

.portfolio-title {
  font-family: var(--font-display);
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--space-2);
  line-height: 1.3;
}

.portfolio-description {
  color: var(--text-secondary);
  line-height: 1.6;
  margin-bottom: var(--space-4);
}

.portfolio-tags {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-2);
}

.portfolio-tag {
  padding: var(--space-1) var(--space-2);
  background: var(--background-secondary);
  color: var(--text-secondary);
  font-size: 0.75rem;
  font-weight: 500;
  border-radius: var(--radius-md);
  border: 1px solid var(--border-light);
}

/* Case Studies Section */
.case-studies-section {
  padding: var(--space-24) 0;
  background: var(--background-secondary);
}

.case-studies-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
  gap: var(--space-12);
  margin-top: var(--space-16);
}

.case-study-item {
  background: var(--background);
  border-radius: var(--radius-2xl);
  padding: var(--space-8);
  box-shadow: var(--shadow-lg);
  transition: var(--transition-medium);
  position: relative;
  overflow: hidden;
}

.case-study-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-500), var(--accent-500));
}

.case-study-item:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-xl);
}

.case-study-header {
  display: flex;
  align-items: center;
  gap: var(--space-4);
  margin-bottom: var(--space-6);
}

.case-study-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, var(--primary-100), var(--primary-200));
  border-radius: var(--radius-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
}

.case-study-meta h3 {
  font-family: var(--font-display);
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--space-1);
}

.case-study-meta p {
  color: var(--text-secondary);
  font-size: 0.95rem;
}

.case-study-description {
  color: var(--text-secondary);
  line-height: 1.6;
  margin-bottom: var(--space-6);
}

.case-study-link {
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
  color: var(--primary-600);
  font-weight: 600;
  text-decoration: none;
  transition: var(--transition-fast);
}

.case-study-link:hover {
  color: var(--primary-700);
  transform: translateX(5px);
}

/* Skills Showcase Section */
.skills-showcase {
  padding: var(--space-24) 0;
}

.skills-categories {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-8);
  margin-top: var(--space-16);
}

.skill-category {
  background: var(--background);
  border-radius: var(--radius-2xl);
  padding: var(--space-8);
  box-shadow: var(--shadow-md);
  transition: var(--transition-medium);
  position: relative;
  overflow: hidden;
}

.skill-category::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, var(--primary-50), var(--accent-50));
  opacity: 0;
  transition: var(--transition-medium);
}

.skill-category:hover::before {
  opacity: 1;
}

.skill-category:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-xl);
}

.category-title {
  font-family: var(--font-display);
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--space-6);
  position: relative;
  z-index: 2;
}

.skills-list {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-3);
  position: relative;
  z-index: 2;
}

.skill-tag {
  padding: var(--space-2) var(--space-4);
  background: var(--background-secondary);
  color: var(--text-primary);
  font-size: 0.875rem;
  font-weight: 600;
  border-radius: var(--radius-lg);
  border: 2px solid var(--border-light);
  transition: var(--transition-medium);
  cursor: pointer;
}

.skill-tag:hover {
  background: var(--primary-500);
  color: white;
  border-color: var(--primary-500);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

/* Portfolio CTA Section */
.portfolio-cta {
  padding: var(--space-24) 0;
  background: linear-gradient(135deg, var(--primary-600), var(--accent-500));
  color: white;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.portfolio-cta::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(circle at 20% 80%, rgba(255,255,255,0.1) 2px, transparent 2px),
    radial-gradient(circle at 80% 20%, rgba(255,255,255,0.1) 1px, transparent 1px);
  background-size: 50px 50px, 30px 30px;
  animation: patternFloat 15s linear infinite;
}

.cta-content {
  position: relative;
  z-index: 2;
  max-width: 600px;
  margin: 0 auto;
}

.cta-title {
  font-family: var(--font-display);
  font-size: clamp(2.5rem, 5vw, 4rem);
  font-weight: 800;
  margin-bottom: var(--space-6);
  line-height: 1.2;
}

.cta-description {
  font-size: 1.25rem;
  margin-bottom: var(--space-10);
  opacity: 0.9;
  line-height: 1.6;
}

.cta-buttons {
  display: flex;
  justify-content: center;
  gap: var(--space-4);
  flex-wrap: wrap;
}

.btn-large {
  padding: var(--space-4) var(--space-8);
  font-size: 1.125rem;
  border-radius: var(--radius-xl);
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
  transition: var(--transition-medium);
}

.btn-primary.btn-large {
  background: white;
  color: var(--primary-600);
  border: 2px solid white;
}

.btn-primary.btn-large:hover {
  background: transparent;
  color: white;
  transform: translateY(-3px);
  box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

.btn-secondary.btn-large {
  background: transparent;
  color: white;
  border: 2px solid rgba(255,255,255,0.3);
}

.btn-secondary.btn-large:hover {
  background: rgba(255,255,255,0.1);
  border-color: white;
  transform: translateY(-3px);
}

.btn-icon {
  font-size: 1.25rem;
  transition: var(--transition-fast);
}

.btn:hover .btn-icon {
  transform: translateX(5px);
}

/* Animation Classes */
.fade-in-up {
  opacity: 0;
  transform: translateY(30px);
}

.fade-in-left {
  opacity: 0;
  transform: translateX(-30px);
}

.fade-in-right {
  opacity: 0;
  transform: translateX(30px);
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero-stats {
    gap: var(--space-6);
  }

  .hero-stats .stat-number {
    font-size: 2rem;
  }

  .portfolio-filters {
    gap: var(--space-2);
  }

  .filter-btn {
    padding: var(--space-3) var(--space-4);
    font-size: 0.875rem;
  }

  .portfolio-grid {
    grid-template-columns: 1fr;
    gap: var(--space-6);
  }

  .case-studies-grid {
    grid-template-columns: 1fr;
    gap: var(--space-8);
  }

  .skills-categories {
    grid-template-columns: 1fr;
    gap: var(--space-6);
  }

  .cta-buttons {
    flex-direction: column;
    align-items: center;
  }

  .btn-large {
    width: 100%;
    max-width: 300px;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .portfolio-filters {
    flex-direction: column;
    align-items: center;
  }

  .filter-btn {
    width: 100%;
    max-width: 250px;
    justify-content: center;
  }

  .hero-stats {
    flex-direction: column;
    gap: var(--space-4);
  }
}
