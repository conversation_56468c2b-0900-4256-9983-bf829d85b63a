// ===== REVOLUTIONARY PARTICLE SYSTEM =====

class ParticleSystem {
    constructor() {
        this.container = document.getElementById('particle-system');
        this.particles = [];
        this.mouse = { x: 0, y: 0 };
        this.isActive = true;
        this.config = {
            particleCount: 50,
            maxParticles: 100,
            particleSize: { min: 1, max: 4 },
            speed: { min: 0.5, max: 2 },
            opacity: { min: 0.1, max: 0.6 },
            colors: ['#ffc321', '#6366f1', '#ec4899', '#06b6d4'],
            connectionDistance: 150,
            mouseInfluenceRadius: 200
        };
        
        if (this.container) {
            this.init();
        }
    }

    init() {
        this.setupCanvas();
        this.createParticles();
        this.setupEventListeners();
        this.animate();
    }

    setupCanvas() {
        this.canvas = document.createElement('canvas');
        this.ctx = this.canvas.getContext('2d');
        this.canvas.style.position = 'absolute';
        this.canvas.style.top = '0';
        this.canvas.style.left = '0';
        this.canvas.style.width = '100%';
        this.canvas.style.height = '100%';
        this.canvas.style.pointerEvents = 'none';
        this.canvas.style.zIndex = '1';
        
        this.container.appendChild(this.canvas);
        this.handleResize();
    }

    createParticles() {
        for (let i = 0; i < this.config.particleCount; i++) {
            this.particles.push(this.createParticle());
        }
    }

    createParticle() {
        return {
            x: Math.random() * this.canvas.width,
            y: Math.random() * this.canvas.height,
            vx: (Math.random() - 0.5) * this.config.speed.max,
            vy: (Math.random() - 0.5) * this.config.speed.max,
            size: Math.random() * (this.config.particleSize.max - this.config.particleSize.min) + this.config.particleSize.min,
            opacity: Math.random() * (this.config.opacity.max - this.config.opacity.min) + this.config.opacity.min,
            color: this.config.colors[Math.floor(Math.random() * this.config.colors.length)],
            life: 1,
            maxLife: Math.random() * 200 + 100,
            angle: Math.random() * Math.PI * 2,
            angleSpeed: (Math.random() - 0.5) * 0.02,
            originalSize: 0,
            pulsing: Math.random() > 0.7,
            pulseSpeed: Math.random() * 0.05 + 0.01
        };
    }

    setupEventListeners() {
        window.addEventListener('mousemove', (e) => {
            this.mouse.x = e.clientX;
            this.mouse.y = e.clientY;
        });

        window.addEventListener('resize', () => {
            this.handleResize();
        });

        // Touch events for mobile
        window.addEventListener('touchmove', (e) => {
            if (e.touches.length > 0) {
                this.mouse.x = e.touches[0].clientX;
                this.mouse.y = e.touches[0].clientY;
            }
        });

        // Click to add particles
        window.addEventListener('click', (e) => {
            this.addParticlesBurst(e.clientX, e.clientY, 5);
        });

        // Scroll-based effects
        window.addEventListener('scroll', () => {
            const scrollPercent = window.scrollY / (document.documentElement.scrollHeight - window.innerHeight);
            this.updateParticleIntensity(scrollPercent);
        });
    }

    animate() {
        if (!this.isActive) return;

        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
        
        this.updateParticles();
        this.drawParticles();
        this.drawConnections();
        
        requestAnimationFrame(() => this.animate());
    }

    updateParticles() {
        for (let i = this.particles.length - 1; i >= 0; i--) {
            const particle = this.particles[i];
            
            // Update position
            particle.x += particle.vx;
            particle.y += particle.vy;
            
            // Update angle for rotation
            particle.angle += particle.angleSpeed;
            
            // Pulsing effect
            if (particle.pulsing) {
                particle.size = particle.originalSize + Math.sin(Date.now() * particle.pulseSpeed) * 0.5;
            }
            
            // Mouse interaction
            const dx = this.mouse.x - particle.x;
            const dy = this.mouse.y - particle.y;
            const distance = Math.sqrt(dx * dx + dy * dy);
            
            if (distance < this.config.mouseInfluenceRadius) {
                const force = (this.config.mouseInfluenceRadius - distance) / this.config.mouseInfluenceRadius;
                const angle = Math.atan2(dy, dx);
                
                // Repel particles from mouse
                particle.vx -= Math.cos(angle) * force * 0.5;
                particle.vy -= Math.sin(angle) * force * 0.5;
                
                // Increase opacity near mouse
                particle.opacity = Math.min(1, particle.opacity + force * 0.3);
            } else {
                // Return to normal opacity
                particle.opacity *= 0.99;
                particle.opacity = Math.max(this.config.opacity.min, particle.opacity);
            }
            
            // Boundary collision with bounce
            if (particle.x < 0 || particle.x > this.canvas.width) {
                particle.vx *= -0.8;
                particle.x = Math.max(0, Math.min(this.canvas.width, particle.x));
            }
            if (particle.y < 0 || particle.y > this.canvas.height) {
                particle.vy *= -0.8;
                particle.y = Math.max(0, Math.min(this.canvas.height, particle.y));
            }
            
            // Apply friction
            particle.vx *= 0.99;
            particle.vy *= 0.99;
            
            // Update life
            particle.life--;
            
            // Remove dead particles
            if (particle.life <= 0) {
                this.particles.splice(i, 1);
            }
        }
        
        // Maintain particle count
        while (this.particles.length < this.config.particleCount) {
            this.particles.push(this.createParticle());
        }
    }

    drawParticles() {
        this.particles.forEach(particle => {
            this.ctx.save();
            
            // Move to particle position
            this.ctx.translate(particle.x, particle.y);
            this.ctx.rotate(particle.angle);
            
            // Set particle style
            this.ctx.globalAlpha = particle.opacity;
            this.ctx.fillStyle = particle.color;
            
            // Create gradient for glow effect
            const gradient = this.ctx.createRadialGradient(0, 0, 0, 0, 0, particle.size * 2);
            gradient.addColorStop(0, particle.color);
            gradient.addColorStop(0.5, particle.color + '80');
            gradient.addColorStop(1, 'transparent');
            
            this.ctx.fillStyle = gradient;
            
            // Draw particle
            this.ctx.beginPath();
            this.ctx.arc(0, 0, particle.size, 0, Math.PI * 2);
            this.ctx.fill();
            
            // Add sparkle effect for some particles
            if (Math.random() > 0.95) {
                this.ctx.strokeStyle = particle.color;
                this.ctx.lineWidth = 1;
                this.ctx.beginPath();
                this.ctx.moveTo(-particle.size, 0);
                this.ctx.lineTo(particle.size, 0);
                this.ctx.moveTo(0, -particle.size);
                this.ctx.lineTo(0, particle.size);
                this.ctx.stroke();
            }
            
            this.ctx.restore();
        });
    }

    drawConnections() {
        for (let i = 0; i < this.particles.length; i++) {
            for (let j = i + 1; j < this.particles.length; j++) {
                const particleA = this.particles[i];
                const particleB = this.particles[j];
                
                const dx = particleA.x - particleB.x;
                const dy = particleA.y - particleB.y;
                const distance = Math.sqrt(dx * dx + dy * dy);
                
                if (distance < this.config.connectionDistance) {
                    const opacity = (1 - distance / this.config.connectionDistance) * 0.3;
                    
                    this.ctx.save();
                    this.ctx.globalAlpha = opacity;
                    this.ctx.strokeStyle = '#ffc321';
                    this.ctx.lineWidth = 1;
                    this.ctx.beginPath();
                    this.ctx.moveTo(particleA.x, particleA.y);
                    this.ctx.lineTo(particleB.x, particleB.y);
                    this.ctx.stroke();
                    this.ctx.restore();
                }
            }
        }
    }

    addParticlesBurst(x, y, count = 10) {
        for (let i = 0; i < count; i++) {
            const particle = this.createParticle();
            particle.x = x + (Math.random() - 0.5) * 50;
            particle.y = y + (Math.random() - 0.5) * 50;
            particle.vx = (Math.random() - 0.5) * 5;
            particle.vy = (Math.random() - 0.5) * 5;
            particle.life = 100;
            particle.opacity = 0.8;
            
            this.particles.push(particle);
        }
    }

    updateParticleIntensity(intensity) {
        this.config.particleCount = Math.floor(50 + intensity * 50);
        this.particles.forEach(particle => {
            particle.opacity *= (0.5 + intensity * 0.5);
        });
    }

    handleResize() {
        this.canvas.width = window.innerWidth;
        this.canvas.height = window.innerHeight;
        
        // Redistribute particles
        this.particles.forEach(particle => {
            if (particle.x > this.canvas.width) particle.x = this.canvas.width;
            if (particle.y > this.canvas.height) particle.y = this.canvas.height;
        });
    }

    // Public methods for external control
    pause() {
        this.isActive = false;
    }

    resume() {
        this.isActive = true;
        this.animate();
    }

    setConfig(newConfig) {
        this.config = { ...this.config, ...newConfig };
    }

    addCustomParticle(config) {
        const particle = { ...this.createParticle(), ...config };
        this.particles.push(particle);
    }

    clearParticles() {
        this.particles = [];
    }

    destroy() {
        this.isActive = false;
        if (this.canvas && this.container) {
            this.container.removeChild(this.canvas);
        }
        
        // Remove event listeners
        window.removeEventListener('mousemove', this.handleMouseMove);
        window.removeEventListener('resize', this.handleResize);
        window.removeEventListener('touchmove', this.handleTouchMove);
        window.removeEventListener('click', this.handleClick);
        window.removeEventListener('scroll', this.handleScroll);
    }
}

// Particle Effects Manager
class ParticleEffectsManager {
    constructor(particleSystem) {
        this.particleSystem = particleSystem;
        this.effects = [];
    }

    createFirework(x, y) {
        const colors = ['#ffc321', '#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4'];
        const particleCount = 20;
        
        for (let i = 0; i < particleCount; i++) {
            const angle = (Math.PI * 2 * i) / particleCount;
            const velocity = Math.random() * 5 + 2;
            
            this.particleSystem.addCustomParticle({
                x: x,
                y: y,
                vx: Math.cos(angle) * velocity,
                vy: Math.sin(angle) * velocity,
                color: colors[Math.floor(Math.random() * colors.length)],
                size: Math.random() * 3 + 2,
                life: 60,
                opacity: 1
            });
        }
    }

    createTrail(startX, startY, endX, endY) {
        const steps = 10;
        const dx = (endX - startX) / steps;
        const dy = (endY - startY) / steps;
        
        for (let i = 0; i < steps; i++) {
            setTimeout(() => {
                this.particleSystem.addCustomParticle({
                    x: startX + dx * i,
                    y: startY + dy * i,
                    vx: 0,
                    vy: 0,
                    color: '#ffc321',
                    size: 2,
                    life: 30,
                    opacity: 0.8
                });
            }, i * 50);
        }
    }
}

// Initialize Particle System
window.addEventListener('DOMContentLoaded', () => {
    try {
        window.ParticleSystem = ParticleSystem;
        window.particleSystem = new ParticleSystem();
        window.particleEffectsManager = new ParticleEffectsManager(window.particleSystem);
        
        console.log('Particle System initialized successfully');
    } catch (error) {
        console.warn('Particle System initialization failed:', error);
    }
});

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { ParticleSystem, ParticleEffectsManager };
}
