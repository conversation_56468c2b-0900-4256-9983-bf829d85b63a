<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Grumpy Company - Create Anything</title>
    <meta name="description" content="Grumpy Company - Revolutionary design & development that pushes boundaries. 5+ years of creating extraordinary digital experiences.">
    <link rel="icon" type="image/png" href="images/favicon.png">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    
    <!-- CSS -->
    <link rel="stylesheet" href="css/style.css">
    
    <!-- GSAP -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/ScrollTrigger.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/TextPlugin.min.js"></script>
</head>
<body>
    <!-- Cursor -->
    <div class="cursor" id="cursor"></div>
    <div class="cursor-follower" id="cursor-follower"></div>

    <!-- Navigation -->
    <nav class="nav">
        <div class="nav-container">
            <div class="nav-logo">
                <img src="images/logo-black.png" alt="Grumpy Company" class="logo">
            </div>
            <div class="nav-links">
                <a href="#work" class="nav-link">Work</a>
                <a href="#services" class="nav-link">Services</a>
                <a href="#about" class="nav-link">About</a>
                <a href="contact.html" class="nav-link nav-cta">Start Project</a>
            </div>
            <div class="nav-toggle" id="nav-toggle">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero">
        <div class="hero-bg">
            <div class="hero-shapes">
                <div class="shape shape-1"></div>
                <div class="shape shape-2"></div>
                <div class="shape shape-3"></div>
                <div class="shape shape-4"></div>
            </div>
        </div>
        
        <div class="hero-content">
            <div class="hero-badge">
                <span>5+ Years of Excellence</span>
            </div>
            
            <h1 class="hero-title">
                <div class="title-line">
                    <span class="word" data-word="Create">Create</span>
                </div>
                <div class="title-line">
                    <span class="word morphing-word" data-word="Anything">
                        <span class="char">A</span>
                        <span class="char">n</span>
                        <span class="char">y</span>
                        <span class="char">t</span>
                        <span class="char">h</span>
                        <span class="char">i</span>
                        <span class="char">n</span>
                        <span class="char">g</span>
                    </span>
                </div>
            </h1>
            
            <div class="hero-subtitle">
                <p class="subtitle-line">Grumpy Company – A wildly creative design studio</p>
                <p class="subtitle-line">built for ambitious brands and visionary projects</p>
            </div>
            
            <div class="hero-actions">
                <button class="btn btn-primary" id="explore-btn">
                    <span class="btn-text">Explore Our Work</span>
                    <span class="btn-arrow">→</span>
                </button>
                <button class="btn btn-secondary" id="contact-btn">
                    <span class="btn-text">Start Your Project</span>
                </button>
            </div>
        </div>
        
        <div class="hero-scroll">
            <div class="scroll-text">Scroll to explore</div>
            <div class="scroll-line"></div>
        </div>
    </section>

    <!-- Why Section -->
    <section class="why-section">
        <div class="container">
            <div class="why-header">
                <h2 class="section-title">
                    <span class="title-word">Why</span>
                    <span class="title-word highlight">Grumpy</span>
                    <span class="title-word">Company</span>
                </h2>
            </div>
            
            <div class="why-content">
                <div class="why-text">
                    <p class="why-description">
                        Grumpy Company allows you to 
                        <span class="highlight-text">effortlessly transform</span> 
                        any vision into reality. Delivering 
                        <span class="highlight-text">mind-blowing results</span> 
                        and unmatched creativity so you can focus on growing your business.
                    </p>
                </div>
                
                <div class="why-features">
                    <div class="feature-item">
                        <div class="feature-icon">🚀</div>
                        <div class="feature-content">
                            <h3 class="feature-title">Create Anything</h3>
                            <p class="feature-desc">From websites to brands, we bring any vision to life</p>
                        </div>
                    </div>
                    
                    <div class="feature-item">
                        <div class="feature-icon">⚡</div>
                        <div class="feature-content">
                            <h3 class="feature-title">Lightning Fast</h3>
                            <p class="feature-desc">Rapid delivery without compromising on quality</p>
                        </div>
                    </div>
                    
                    <div class="feature-item">
                        <div class="feature-icon">🎯</div>
                        <div class="feature-content">
                            <h3 class="feature-title">Pixel Perfect</h3>
                            <p class="feature-desc">Obsessive attention to every detail and interaction</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Services Section -->
    <section class="services-section" id="services">
        <div class="container">
            <div class="services-header">
                <h2 class="section-title">
                    <span class="title-word">Our</span>
                    <span class="title-word highlight">Expertise</span>
                </h2>
            </div>
            
            <div class="services-grid">
                <div class="service-card" data-service="web">
                    <div class="service-number">01</div>
                    <div class="service-content">
                        <h3 class="service-title">Web Design & Development</h3>
                        <p class="service-desc">Revolutionary websites that push boundaries and deliver exceptional user experiences</p>
                        <div class="service-tags">
                            <span class="tag">React</span>
                            <span class="tag">Next.js</span>
                            <span class="tag">GSAP</span>
                            <span class="tag">WebGL</span>
                        </div>
                    </div>
                    <div class="service-arrow">→</div>
                </div>
                
                <div class="service-card" data-service="design">
                    <div class="service-number">02</div>
                    <div class="service-content">
                        <h3 class="service-title">Graphic Design</h3>
                        <p class="service-desc">Visual storytelling that captures attention and creates lasting brand impressions</p>
                        <div class="service-tags">
                            <span class="tag">Branding</span>
                            <span class="tag">Print</span>
                            <span class="tag">Digital</span>
                            <span class="tag">Motion</span>
                        </div>
                    </div>
                    <div class="service-arrow">→</div>
                </div>
                
                <div class="service-card" data-service="wedding">
                    <div class="service-number">03</div>
                    <div class="service-content">
                        <h3 class="service-title">Wedding Design</h3>
                        <p class="service-desc">Elegant and romantic design solutions that make your special day unforgettable</p>
                        <div class="service-tags">
                            <span class="tag">Invitations</span>
                            <span class="tag">Websites</span>
                            <span class="tag">Branding</span>
                            <span class="tag">Print</span>
                        </div>
                    </div>
                    <div class="service-arrow">→</div>
                </div>
                
                <div class="service-card" data-service="development">
                    <div class="service-number">04</div>
                    <div class="service-content">
                        <h3 class="service-title">Development Services</h3>
                        <p class="service-desc">Full-stack solutions using cutting-edge technologies for scalable applications</p>
                        <div class="service-tags">
                            <span class="tag">Full-Stack</span>
                            <span class="tag">APIs</span>
                            <span class="tag">Cloud</span>
                            <span class="tag">Mobile</span>
                        </div>
                    </div>
                    <div class="service-arrow">→</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Work Section -->
    <section class="work-section" id="work">
        <div class="container">
            <div class="work-header">
                <h2 class="section-title">
                    <span class="title-word">Featured</span>
                    <span class="title-word highlight">Work</span>
                </h2>
                <p class="section-subtitle">Projects that showcase our commitment to excellence and innovation</p>
            </div>

            <div class="work-grid">
                <div class="work-item" data-project="ecommerce">
                    <div class="work-image">
                        <div class="work-preview">
                            <div class="preview-bg" style="background: linear-gradient(135deg, #ffc321 0%, #ff6b6b 100%);"></div>
                            <div class="preview-content">
                                <div class="preview-title">E-Commerce Platform</div>
                                <div class="preview-subtitle">Modern Shopping Experience</div>
                            </div>
                        </div>
                    </div>
                    <div class="work-info">
                        <h3 class="work-title">Revolutionary E-Commerce</h3>
                        <p class="work-desc">Complete platform with advanced features and seamless UX</p>
                        <div class="work-tags">
                            <span class="tag">React</span>
                            <span class="tag">Node.js</span>
                            <span class="tag">Stripe</span>
                        </div>
                    </div>
                </div>

                <div class="work-item" data-project="brand">
                    <div class="work-image">
                        <div class="work-preview">
                            <div class="preview-bg" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);"></div>
                            <div class="preview-content">
                                <div class="preview-title">Brand Identity</div>
                                <div class="preview-subtitle">Luxury Fashion Brand</div>
                            </div>
                        </div>
                    </div>
                    <div class="work-info">
                        <h3 class="work-title">Complete Brand Identity</h3>
                        <p class="work-desc">Full branding package for luxury fashion startup</p>
                        <div class="work-tags">
                            <span class="tag">Branding</span>
                            <span class="tag">Logo</span>
                            <span class="tag">Print</span>
                        </div>
                    </div>
                </div>

                <div class="work-item" data-project="wedding">
                    <div class="work-image">
                        <div class="work-preview">
                            <div class="preview-bg" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);"></div>
                            <div class="preview-content">
                                <div class="preview-title">Wedding Suite</div>
                                <div class="preview-subtitle">Elegant Design Package</div>
                            </div>
                        </div>
                    </div>
                    <div class="work-info">
                        <h3 class="work-title">Wedding Design Suite</h3>
                        <p class="work-desc">Complete wedding package with invitations and website</p>
                        <div class="work-tags">
                            <span class="tag">Wedding</span>
                            <span class="tag">Print</span>
                            <span class="tag">Web</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="work-cta">
                <a href="portfolio.html" class="btn btn-outline">
                    <span class="btn-text">View All Projects</span>
                    <span class="btn-arrow">→</span>
                </a>
            </div>
        </div>
    </section>

    <!-- Stats Section -->
    <section class="stats-section">
        <div class="container">
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-number" data-count="150">0</div>
                    <div class="stat-label">Projects Completed</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" data-count="5">0</div>
                    <div class="stat-label">Years Experience</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" data-count="98">0</div>
                    <div class="stat-label">Client Satisfaction</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" data-count="25">0</div>
                    <div class="stat-label">Awards Won</div>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="cta-section">
        <div class="container">
            <div class="cta-content">
                <h2 class="cta-title">
                    <span class="title-word">Ready to</span>
                    <span class="title-word highlight">Create</span>
                    <span class="title-word">Something</span>
                    <span class="title-word highlight">Amazing?</span>
                </h2>
                <p class="cta-subtitle">Let's transform your vision into reality</p>
                <div class="cta-actions">
                    <a href="contact.html" class="btn btn-primary btn-large">
                        <span class="btn-text">Start Your Project</span>
                        <span class="btn-arrow">→</span>
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Scripts -->
    <script src="js/main.js"></script>
</body>
</html>
