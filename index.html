<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Grumpy Company - Mind-Blowing Design & Development</title>
    <meta name="description" content="Grumpy Company - 5+ years of experience in revolutionary website design, development, graphic design, wedding design, and premium development services.">
    <link rel="icon" type="image/png" href="images/favicon.png">

    <!-- Preconnect to external domains -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="preconnect" href="https://cdnjs.cloudflare.com">

    <!-- CSS -->
    <link rel="stylesheet" href="css/style.css">

    <!-- GSAP -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/ScrollTrigger.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/TextPlugin.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/MorphSVGPlugin.min.js"></script>
</head>
<body>
    <!-- Loading Screen -->
    <div class="loader-overlay" id="loader">
        <div class="loader-content">
            <div class="loader-logo">
                <img src="images/logo-black.png" alt="Grumpy Company" class="logo-img">
            </div>
            <div class="loader-progress">
                <div class="progress-line"></div>
            </div>
            <div class="loader-text">Crafting Experience</div>
        </div>
    </div>

    <!-- Morphing Background -->
    <div class="morphing-bg"></div>

    <!-- Floating Particles -->
    <div class="particles-container" id="particles"></div>

    <!-- Navigation -->
    <nav class="navbar" id="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <img src="images/logo-black.png" alt="Grumpy Company" class="nav-logo-img">
            </div>
            <ul class="nav-menu" id="nav-menu">
                <li><a href="#home" class="nav-link">Home</a></li>
                <li><a href="#services" class="nav-link">Services</a></li>
                <li><a href="#work" class="nav-link">Work</a></li>
                <li><a href="#about" class="nav-link">About</a></li>
                <li><a href="#contact" class="nav-link contact-btn">Contact</a></li>
            </ul>
            <div class="hamburger" id="hamburger">
                <span class="bar"></span>
                <span class="bar"></span>
                <span class="bar"></span>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero" id="home">
        <div class="hero-background">
            <div class="gradient-orb orb-1"></div>
            <div class="gradient-orb orb-2"></div>
            <div class="gradient-orb orb-3"></div>
            <div class="grid-pattern"></div>
        </div>

        <div class="container">
            <div class="hero-content">
                <h1 class="hero-title">
                    <span class="hero-title-main">We Create</span>
                    <span class="hero-title-highlight">
                        <span class="morphing-word" id="morphing-text">Extraordinary</span>
                    </span>
                    <span class="hero-title-main">Digital Experiences</span>
                </h1>

                <p class="hero-subtitle">
                    Transforming ideas into mind-blowing digital realities with 5+ years of expertise in
                    cutting-edge design, development, and creative solutions that push boundaries.
                </p>

                <div class="hero-cta">
                    <a href="#work" class="cta-primary">
                        <span>Explore Our Work</span>
                    </a>
                    <a href="#contact" class="cta-secondary">
                        <span>Start Your Project</span>
                    </a>
                </div>

                <div class="hero-stats">
                    <div class="stat-item">
                        <div class="stat-number gradient-text" data-count="150">0</div>
                        <div class="stat-label">Projects Completed</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number gradient-text" data-count="5">0</div>
                        <div class="stat-label">Years Experience</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number gradient-text" data-count="100">0</div>
                        <div class="stat-label">Happy Clients</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Scroll Indicator -->
        <div class="scroll-indicator">
            <div class="scroll-line"></div>
            <div class="scroll-text">Scroll to Explore</div>
        </div>
    </section>

    <!-- Services Section -->
    <section class="services section" id="services">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">
                    <span class="gradient-text">Our Expertise</span>
                </h2>
                <p class="section-subtitle">
                    We specialize in creating exceptional digital experiences that captivate,
                    engage, and convert your audience.
                </p>
            </div>

            <div class="services-grid">
                <div class="service-card glass morph-shape">
                    <div class="service-icon">
                        <svg class="icon-svg" viewBox="0 0 24 24" fill="none">
                            <path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="currentColor" stroke-width="2"/>
                            <path d="M2 17L12 22L22 17" stroke="currentColor" stroke-width="2"/>
                            <path d="M2 12L12 17L22 12" stroke="currentColor" stroke-width="2"/>
                        </svg>
                    </div>
                    <h3 class="service-title">Web Design & Development</h3>
                    <p class="service-description">
                        Cutting-edge websites that blend stunning visuals with flawless functionality,
                        optimized for performance and user experience.
                    </p>
                    <div class="service-features">
                        <span class="feature-tag">Responsive Design</span>
                        <span class="feature-tag">Performance Optimization</span>
                        <span class="feature-tag">Modern Frameworks</span>
                    </div>
                </div>

                <div class="service-card glass morph-shape">
                    <div class="service-icon">
                        <svg class="icon-svg" viewBox="0 0 24 24" fill="none">
                            <rect x="3" y="3" width="18" height="18" rx="2" ry="2" stroke="currentColor" stroke-width="2"/>
                            <circle cx="9" cy="9" r="2" stroke="currentColor" stroke-width="2"/>
                            <path d="M21 15L16 10L5 21" stroke="currentColor" stroke-width="2"/>
                        </svg>
                    </div>
                    <h3 class="service-title">Graphic Design</h3>
                    <p class="service-description">
                        Visual storytelling that captures your brand essence through innovative
                        design solutions that stand out in the digital landscape.
                    </p>
                    <div class="service-features">
                        <span class="feature-tag">Brand Identity</span>
                        <span class="feature-tag">Print Design</span>
                        <span class="feature-tag">Digital Assets</span>
                    </div>
                </div>

                <div class="service-card glass morph-shape">
                    <div class="service-icon">
                        <svg class="icon-svg" viewBox="0 0 24 24" fill="none">
                            <path d="M20.84 4.61A5.5 5.5 0 0 0 15.5 3H8.5A5.5 5.5 0 0 0 3.16 4.61L12 21L20.84 4.61Z" stroke="currentColor" stroke-width="2"/>
                        </svg>
                    </div>
                    <h3 class="service-title">Wedding Design</h3>
                    <p class="service-description">
                        Elegant and romantic design solutions for your special day, creating
                        memorable experiences through beautiful visual narratives.
                    </p>
                    <div class="service-features">
                        <span class="feature-tag">Invitations</span>
                        <span class="feature-tag">Digital Assets</span>
                        <span class="feature-tag">Event Branding</span>
                    </div>
                </div>

                <div class="service-card glass morph-shape">
                    <div class="service-icon">
                        <svg class="icon-svg" viewBox="0 0 24 24" fill="none">
                            <polyline points="16 18 22 12 16 6" stroke="currentColor" stroke-width="2"/>
                            <polyline points="8 6 2 12 8 18" stroke="currentColor" stroke-width="2"/>
                        </svg>
                    </div>
                    <h3 class="service-title">Development Services</h3>
                    <p class="service-description">
                        Full-stack development solutions using the latest technologies to build
                        scalable, secure, and high-performance applications.
                    </p>
                    <div class="service-features">
                        <span class="feature-tag">Full-Stack</span>
                        <span class="feature-tag">API Development</span>
                        <span class="feature-tag">Cloud Solutions</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Work Section -->
    <section class="work section" id="work">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">
                    <span class="gradient-text">Featured Work</span>
                </h2>
                <p class="section-subtitle">
                    A showcase of our most innovative and impactful projects that demonstrate
                    our commitment to excellence and creativity.
                </p>
            </div>

            <div class="work-grid">
                <div class="work-item glass">
                    <div class="work-image">
                        <div class="work-placeholder">
                            <svg viewBox="0 0 400 300" class="placeholder-svg">
                                <rect width="400" height="300" fill="url(#gradient1)"/>
                                <defs>
                                    <linearGradient id="gradient1" x1="0%" y1="0%" x2="100%" y2="100%">
                                        <stop offset="0%" style="stop-color:#ffc321;stop-opacity:0.8" />
                                        <stop offset="100%" style="stop-color:#6366f1;stop-opacity:0.8" />
                                    </linearGradient>
                                </defs>
                                <text x="200" y="150" text-anchor="middle" fill="white" font-size="24" font-weight="bold">E-Commerce Platform</text>
                            </svg>
                        </div>
                        <div class="work-overlay">
                            <div class="work-info">
                                <h3 class="work-title">Modern E-Commerce</h3>
                                <p class="work-description">Full-stack e-commerce solution with advanced features</p>
                                <div class="work-tags">
                                    <span class="work-tag">React</span>
                                    <span class="work-tag">Node.js</span>
                                    <span class="work-tag">MongoDB</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="work-item glass">
                    <div class="work-image">
                        <div class="work-placeholder">
                            <svg viewBox="0 0 400 300" class="placeholder-svg">
                                <rect width="400" height="300" fill="url(#gradient2)"/>
                                <defs>
                                    <linearGradient id="gradient2" x1="0%" y1="0%" x2="100%" y2="100%">
                                        <stop offset="0%" style="stop-color:#ec4899;stop-opacity:0.8" />
                                        <stop offset="100%" style="stop-color:#06b6d4;stop-opacity:0.8" />
                                    </linearGradient>
                                </defs>
                                <text x="200" y="150" text-anchor="middle" fill="white" font-size="24" font-weight="bold">Brand Identity</text>
                            </svg>
                        </div>
                        <div class="work-overlay">
                            <div class="work-info">
                                <h3 class="work-title">Brand Identity Design</h3>
                                <p class="work-description">Complete brand identity for luxury fashion brand</p>
                                <div class="work-tags">
                                    <span class="work-tag">Branding</span>
                                    <span class="work-tag">Logo Design</span>
                                    <span class="work-tag">Print</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="work-item glass">
                    <div class="work-image">
                        <div class="work-placeholder">
                            <svg viewBox="0 0 400 300" class="placeholder-svg">
                                <rect width="400" height="300" fill="url(#gradient3)"/>
                                <defs>
                                    <linearGradient id="gradient3" x1="0%" y1="0%" x2="100%" y2="100%">
                                        <stop offset="0%" style="stop-color:#10b981;stop-opacity:0.8" />
                                        <stop offset="100%" style="stop-color:#ffc321;stop-opacity:0.8" />
                                    </linearGradient>
                                </defs>
                                <text x="200" y="150" text-anchor="middle" fill="white" font-size="24" font-weight="bold">Wedding Suite</text>
                            </svg>
                        </div>
                        <div class="work-overlay">
                            <div class="work-info">
                                <h3 class="work-title">Wedding Design Suite</h3>
                                <p class="work-description">Elegant wedding invitation and branding package</p>
                                <div class="work-tags">
                                    <span class="work-tag">Wedding</span>
                                    <span class="work-tag">Print Design</span>
                                    <span class="work-tag">Branding</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="work-cta">
                <a href="portfolio.html" class="cta-primary">
                    <span>View All Projects</span>
                </a>
            </div>
        </div>
    </section>

    <!-- About Section -->
    <section class="about section" id="about">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">
                    <span class="gradient-text">About Grumpy Company</span>
                </h2>
                <p class="section-subtitle">
                    With 5+ years of experience, we've mastered the art of creating digital experiences
                    that not only look stunning but deliver exceptional results for our clients.
                </p>
            </div>

            <div class="about-content">
                <div class="about-stats">
                    <div class="stat-card glass">
                        <div class="stat-icon">🚀</div>
                        <div class="stat-value gradient-text" data-count="150">0</div>
                        <div class="stat-text">Projects Launched</div>
                    </div>
                    <div class="stat-card glass">
                        <div class="stat-icon">⭐</div>
                        <div class="stat-value gradient-text" data-count="98">0</div>
                        <div class="stat-text">Client Satisfaction</div>
                    </div>
                    <div class="stat-card glass">
                        <div class="stat-icon">🏆</div>
                        <div class="stat-value gradient-text" data-count="25">0</div>
                        <div class="stat-text">Awards Won</div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section class="contact section" id="contact">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">
                    <span class="gradient-text">Let's Create Something Amazing</span>
                </h2>
                <p class="section-subtitle">
                    Ready to transform your vision into reality? Get in touch and let's discuss
                    how we can bring your project to life.
                </p>
            </div>

            <div class="contact-cta">
                <a href="contact.html" class="cta-primary">
                    <span>Start Your Project</span>
                </a>
                <a href="mailto:<EMAIL>" class="cta-secondary">
                    <span>Send Email</span>
                </a>
            </div>
        </div>
    </section>

    <!-- JavaScript -->
    <script src="js/script.js"></script>
</body>
</html>