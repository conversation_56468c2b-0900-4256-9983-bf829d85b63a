<!DOCTYPE html>
<html lang="en" class="no-js">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Grumpy Company - Revolutionary Digital Experiences</title>
    <meta name="description" content="Grumpy Company - Pushing the boundaries of digital design and development. 5+ years of creating extraordinary experiences.">
    <link rel="icon" type="image/png" href="images/favicon.png">
    
    <!-- Preload Critical Resources -->
    <link rel="preload" href="fonts/BricolageGrotesque-Black.woff2" as="font" type="font/woff2" crossorigin>
    <link rel="preload" href="fonts/BricolageGrotesque-Bold.woff2" as="font" type="font/woff2" crossorigin>
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    
    <!-- Custom Fonts -->
    <style>
        @font-face {
            font-family: 'NewBlackTypeface';
            src: url('fonts/BricolageGrotesque-Black.woff2') format('woff2');
            font-weight: 900;
            font-style: normal;
            font-display: swap;
        }
        @font-face {
            font-family: 'NewBlackTypeface';
            src: url('fonts/BricolageGrotesque-Bold.woff2') format('woff2');
            font-weight: 700;
            font-style: normal;
            font-display: swap;
        }
        @font-face {
            font-family: 'NewBlackTypeface';
            src: url('fonts/BricolageGrotesque-Medium.woff2') format('woff2');
            font-weight: 500;
            font-style: normal;
            font-display: swap;
        }
    </style>
    
    <!-- Critical CSS -->
    <link rel="stylesheet" href="css/main.css">
    
    <!-- Advanced Libraries -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/ScrollTrigger.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/TextPlugin.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/lottie-web/5.12.2/lottie.min.js"></script>
    
    <!-- Performance Optimization -->
    <script>
        document.documentElement.classList.remove('no-js');
        document.documentElement.classList.add('js');
    </script>
</head>
<body>
    <!-- Advanced Loading System -->
    <div class="loader-system" id="loader-system">
        <div class="loader-background">
            <canvas id="loader-canvas"></canvas>
        </div>
        <div class="loader-content">
            <div class="loader-logo">
                <svg class="logo-svg" viewBox="0 0 100 100">
                    <path class="logo-path" d="M20,50 Q50,20 80,50 Q50,80 20,50" fill="none" stroke="#ffc321" stroke-width="2"/>
                </svg>
            </div>
            <div class="loader-text">
                <span class="loader-word" data-text="GRUMPY">GRUMPY</span>
                <span class="loader-word" data-text="COMPANY">COMPANY</span>
            </div>
            <div class="loader-progress">
                <div class="progress-container">
                    <div class="progress-bar"></div>
                    <div class="progress-glow"></div>
                </div>
                <div class="progress-text">
                    <span class="progress-number">0</span>
                    <span class="progress-percent">%</span>
                </div>
            </div>
        </div>
        <div class="loader-particles" id="loader-particles"></div>
    </div>

    <!-- WebGL Background -->
    <canvas id="webgl-canvas" class="webgl-canvas"></canvas>
    
    <!-- Particle System -->
    <div id="particle-system" class="particle-system"></div>
    
    <!-- Navigation -->
    <nav class="nav" id="main-nav">
        <div class="nav-container">
            <div class="nav-logo">
                <img src="images/logo-black.png" alt="Grumpy Company" class="logo">
            </div>
            <div class="nav-menu">
                <a href="#home" class="nav-link" data-text="Home">Home</a>
                <a href="#about" class="nav-link" data-text="About">About</a>
                <a href="#services" class="nav-link" data-text="Services">Services</a>
                <a href="portfolio.html" class="nav-link" data-text="Portfolio">Portfolio</a>
                <a href="contact.html" class="nav-link" data-text="Contact">Contact</a>
            </div>
            <div class="nav-toggle" id="nav-toggle">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero" id="home">
        <div class="hero-background">
            <div class="hero-shapes">
                <div class="shape shape-1"></div>
                <div class="shape shape-2"></div>
                <div class="shape shape-3"></div>
            </div>
        </div>
        <div class="hero-content">
            <div class="hero-badge">
                <span class="badge-text">Revolutionary Digital Experiences</span>
                <div class="badge-glow"></div>
            </div>
            <h1 class="hero-title">
                <span class="title-line">
                    <span class="word" data-text="WE">WE</span>
                    <span class="word" data-text="CREATE">CREATE</span>
                </span>
                <span class="title-line">
                    <span class="word morphing-word" id="morphing-word">
                        <span class="char">T</span>
                        <span class="char">H</span>
                        <span class="char">E</span>
                        <span class="char"> </span>
                        <span class="char">F</span>
                        <span class="char">U</span>
                        <span class="char">T</span>
                        <span class="char">U</span>
                        <span class="char">R</span>
                        <span class="char">E</span>
                    </span>
                </span>
            </h1>
            <p class="hero-subtitle">
                <span class="subtitle-line">Pushing boundaries with cutting-edge design,</span>
                <span class="subtitle-line">advanced development, and revolutionary experiences</span>
                <span class="subtitle-line">that challenge the impossible.</span>
            </p>
            <div class="hero-actions">
                <a href="#services" class="btn btn-primary">
                    <span class="btn-text">Explore Our Work</span>
                    <span class="btn-arrow">→</span>
                    <div class="btn-ripple"></div>
                </a>
                <a href="contact.html" class="btn btn-secondary">
                    <span class="btn-text">Start Project</span>
                    <span class="btn-arrow">↗</span>
                    <div class="btn-ripple"></div>
                </a>
            </div>
        </div>
        <div class="hero-scroll">
            <span class="scroll-text">Scroll to Explore</span>
            <div class="scroll-indicator">
                <div class="scroll-line"></div>
            </div>
        </div>
    </section>

    <!-- About Section -->
    <section class="about" id="about">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">About Grumpy Company</h2>
                <p class="section-subtitle">5+ years of revolutionary digital craftsmanship</p>
            </div>
            <div class="about-content">
                <div class="about-text">
                    <p>We are digital revolutionaries, pushing the boundaries of what's possible in design and development. With over 5 years of experience, we've mastered the art of creating extraordinary digital experiences.</p>
                </div>
                <div class="about-stats">
                    <div class="stat-item">
                        <span class="stat-number" data-target="150">0</span>
                        <span class="stat-label">Projects Completed</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number" data-target="50">0</span>
                        <span class="stat-label">Happy Clients</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number" data-target="5">0</span>
                        <span class="stat-label">Years Experience</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Services Section -->
    <section class="services" id="services">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Our Expertise</h2>
                <p class="section-subtitle">Cutting-edge solutions across multiple disciplines</p>
            </div>
            <div class="services-grid">
                <div class="service-card" data-service="web">
                    <div class="service-icon">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                            <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"/>
                            <polyline points="3.27,6.96 12,12.01 20.73,6.96"/>
                            <line x1="12" y1="22.08" x2="12" y2="12"/>
                        </svg>
                    </div>
                    <h3 class="service-title">Web Development</h3>
                    <p class="service-description">Revolutionary websites with cutting-edge technologies and mind-blowing user experiences.</p>
                    <a href="web-development.html" class="service-link">Explore →</a>
                </div>
                
                <div class="service-card" data-service="graphic">
                    <div class="service-icon">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                            <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
                            <circle cx="8.5" cy="8.5" r="1.5"/>
                            <polyline points="21,15 16,10 5,21"/>
                        </svg>
                    </div>
                    <h3 class="service-title">Graphic Design</h3>
                    <p class="service-description">Stunning visual identities and brand experiences that captivate and inspire.</p>
                    <a href="graphic-design.html" class="service-link">Explore →</a>
                </div>
                
                <div class="service-card" data-service="wedding">
                    <div class="service-icon">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                            <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"/>
                        </svg>
                    </div>
                    <h3 class="service-title">Wedding Design</h3>
                    <p class="service-description">Magical wedding experiences with elegant design and unforgettable moments.</p>
                    <a href="wedding-design.html" class="service-link">Explore →</a>
                </div>
                
                <div class="service-card" data-service="development">
                    <div class="service-icon">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                            <polyline points="16,18 22,12 16,6"/>
                            <polyline points="8,6 2,12 8,18"/>
                        </svg>
                    </div>
                    <h3 class="service-title">Development Services</h3>
                    <p class="service-description">Advanced technical solutions and custom development for complex challenges.</p>
                    <a href="development-services.html" class="service-link">Explore →</a>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact CTA -->
    <section class="cta">
        <div class="container">
            <div class="cta-content">
                <h2 class="cta-title">Ready to Create Something Extraordinary?</h2>
                <p class="cta-subtitle">Let's push the boundaries together</p>
                <a href="contact.html" class="btn btn-primary btn-large">
                    <span class="btn-text">Start Your Project</span>
                    <span class="btn-arrow">→</span>
                    <div class="btn-ripple"></div>
                </a>
            </div>
        </div>
    </section>

    <!-- Scripts -->
    <script src="js/webgl.js"></script>
    <script src="js/particles.js"></script>
    <script src="js/animations.js"></script>
    <script src="js/main.js"></script>
</body>
</html>
